'use strict';
exports.main = async (event, context) => {
  // event 里包含 encryptedData, iv
  try {
    const res = await uniCloud.getPhoneNumber({
      appid: context.APPID,
      encryptedData: event.encryptedData,
      iv: event.iv,
      cloudID: event.cloudID // 兼容不同小程序版本
    })
    if (!res.phoneNumber) {
      return { code: -1, msg: '手机号解密失败' }
    }
    return {
      code: 0,
      msg: '手机号获取成功',
      phoneNumber: res.phoneNumber
    }
  } catch (e) {
    return { code: -2, msg: '云函数异常', error: e.message }
  }
} 