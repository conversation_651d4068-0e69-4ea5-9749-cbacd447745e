<template>
  <view class="test-page">
    <view class="page-header">
      <text class="page-title">订单云函数测试</text>
    </view>
    
    <!-- 创建订单测试 -->
    <view class="test-section">
      <view class="section-title">1. 创建订单测试</view>
      <button type="primary" @click="testCreateOrder" :loading="loading.create">创建测试订单</button>
      <view v-if="createResult" class="result-box">
        <text>创建结果：{{ JSON.stringify(createResult, null, 2) }}</text>
      </view>
    </view>
    
    <!-- 获取订单测试 -->
    <view class="test-section">
      <view class="section-title">2. 获取订单测试</view>
      <input v-model="testOrderId" placeholder="请输入订单ID" class="test-input" />
      <button type="default" @click="testGetOrder" :loading="loading.get">获取订单</button>
      <view v-if="getResult" class="result-box">
        <text>获取结果：{{ JSON.stringify(getResult, null, 2) }}</text>
      </view>
    </view>
    
    <!-- 更新订单测试 -->
    <view class="test-section">
      <view class="section-title">3. 更新订单测试</view>
      <input v-model="updateOrderId" placeholder="请输入订单ID" class="test-input" />
      <button type="default" @click="testUpdateOrder" :loading="loading.update">更新订单</button>
      <view v-if="updateResult" class="result-box">
        <text>更新结果：{{ JSON.stringify(updateResult, null, 2) }}</text>
      </view>
    </view>
    
    <!-- 获取订单列表测试 -->
    <view class="test-section">
      <view class="section-title">4. 获取订单列表测试</view>
      <button type="default" @click="testGetOrderList" :loading="loading.list">获取订单列表</button>
      <view v-if="listResult" class="result-box">
        <text>列表结果：{{ JSON.stringify(listResult, null, 2) }}</text>
      </view>
    </view>
    
    <!-- 更新订单状态测试 -->
    <view class="test-section">
      <view class="section-title">5. 更新订单状态测试</view>
      <input v-model="statusOrderId" placeholder="请输入订单ID" class="test-input" />
      <picker :range="statusOptions" range-key="label" @change="onStatusChange">
        <view class="picker">{{ selectedStatus.label || '选择状态' }}</view>
      </picker>
      <button type="default" @click="testUpdateStatus" :loading="loading.status">更新状态</button>
      <view v-if="statusResult" class="result-box">
        <text>状态更新结果：{{ JSON.stringify(statusResult, null, 2) }}</text>
      </view>
    </view>
    
    <!-- 删除订单测试 -->
    <view class="test-section">
      <view class="section-title">6. 删除订单测试</view>
      <input v-model="deleteOrderId" placeholder="请输入订单ID" class="test-input" />
      <button type="warn" @click="testDeleteOrder" :loading="loading.delete">删除订单</button>
      <view v-if="deleteResult" class="result-box">
        <text>删除结果：{{ JSON.stringify(deleteResult, null, 2) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import OrderService from './order-service.js'
import { yuanToCents } from '@/utils/money.js'

export default {
  data() {
    return {
      loading: {
        create: false,
        get: false,
        update: false,
        list: false,
        status: false,
        delete: false
      },
      
      // 测试数据
      testOrderId: '',
      updateOrderId: '',
      statusOrderId: '',
      deleteOrderId: '',
      
      // 结果数据
      createResult: null,
      getResult: null,
      updateResult: null,
      listResult: null,
      statusResult: null,
      deleteResult: null,
      
      // 状态选择
      statusOptions: [
        { value: 'pending', label: '待支付' },
        { value: 'paid', label: '已支付' },
        { value: 'shipped', label: '已发货' },
        { value: 'completed', label: '已完成' },
        { value: 'cancelled', label: '已取消' },
        { value: 'refunded', label: '已退款' }
      ],
      selectedStatus: {}
    }
  },
  
  methods: {
    // 测试创建订单
    async testCreateOrder() {
      this.loading.create = true
      try {
        const orderData = {
          userId: 'test_user_' + Date.now(),
          // 将元转换为分传给云函数
          totalFee: yuanToCents(299.00),
          goods: [
            {
              goodsId: 'goods001',
              name: '测试商品',
              price: yuanToCents(99.00), // 商品价格也转换为分
              quantity: 3,
              image: 'https://example.com/image.jpg'
            }
          ],
          address: {
            name: '测试用户',
            phone: '13800138000',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            detail: '科技园南区测试地址'
          },
          paymentMethod: 'wechat',
          remark: '这是一个测试订单',
          subject: '测试订单标题',
          // 优惠金额和运费也转换为分
          discountAmount: yuanToCents(10.00),
          shippingFee: yuanToCents(10.00),
          isMock: true // 模拟支付
        }
        
        this.createResult = await OrderService.createOrder(orderData)
        
        // 自动填充订单ID到其他测试框
        if (this.createResult.data && this.createResult.data.orderId) {
          this.testOrderId = this.createResult.data.orderId
          this.updateOrderId = this.createResult.data.orderId
          this.statusOrderId = this.createResult.data.orderId
          this.deleteOrderId = this.createResult.data.orderId
        }
      } catch (error) {
        console.error('创建订单测试失败:', error)
      } finally {
        this.loading.create = false
      }
    },
    
    // 测试获取订单
    async testGetOrder() {
      if (!this.testOrderId) {
        uni.showToast({ title: '请输入订单ID', icon: 'error' })
        return
      }
      
      this.loading.get = true
      try {
        this.getResult = await OrderService.getOrder(this.testOrderId)
      } catch (error) {
        console.error('获取订单测试失败:', error)
        this.getResult = { error: error.message }
      } finally {
        this.loading.get = false
      }
    },
    
    // 测试更新订单
    async testUpdateOrder() {
      if (!this.updateOrderId) {
        uni.showToast({ title: '请输入订单ID', icon: 'error' })
        return
      }
      
      this.loading.update = true
      try {
        const updateData = {
          remark: '订单备注已更新 - ' + new Date().toLocaleString(),
          address: {
            name: '更新后的用户名',
            phone: '13900139000'
          }
        }
        
        this.updateResult = await OrderService.updateOrder(this.updateOrderId, updateData)
      } catch (error) {
        console.error('更新订单测试失败:', error)
        this.updateResult = { error: error.message }
      } finally {
        this.loading.update = false
      }
    },
    
    // 测试获取订单列表
    async testGetOrderList() {
      this.loading.list = true
      try {
        const params = {
          pageNum: 1,
          pageSize: 10,
          sortField: 'createTime',
          sortOrder: 'desc'
        }
        
        this.listResult = await OrderService.getOrderList(params)
      } catch (error) {
        console.error('获取订单列表测试失败:', error)
        this.listResult = { error: error.message }
      } finally {
        this.loading.list = false
      }
    },
    
    // 测试更新订单状态
    async testUpdateStatus() {
      if (!this.statusOrderId) {
        uni.showToast({ title: '请输入订单ID', icon: 'error' })
        return
      }
      if (!this.selectedStatus.value) {
        uni.showToast({ title: '请选择状态', icon: 'error' })
        return
      }
      
      this.loading.status = true
      try {
        this.statusResult = await OrderService.updateOrderStatus(
          this.statusOrderId, 
          this.selectedStatus.value,
          '状态更新测试 - ' + new Date().toLocaleString()
        )
      } catch (error) {
        console.error('更新订单状态测试失败:', error)
        this.statusResult = { error: error.message }
      } finally {
        this.loading.status = false
      }
    },
    
    // 测试删除订单
    async testDeleteOrder() {
      if (!this.deleteOrderId) {
        uni.showToast({ title: '请输入订单ID', icon: 'error' })
        return
      }
      
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该测试订单吗？',
        success: async (res) => {
          if (res.confirm) {
            this.loading.delete = true
            try {
              this.deleteResult = await OrderService.deleteOrder(this.deleteOrderId)
            } catch (error) {
              console.error('删除订单测试失败:', error)
              this.deleteResult = { error: error.message }
            } finally {
              this.loading.delete = false
            }
          }
        }
      })
    },
    
    // 状态选择
    onStatusChange(e) {
      this.selectedStatus = this.statusOptions[e.detail.value]
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 20rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.test-input {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.picker {
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  background: #fff;
}

.result-box {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 24rpx;
  word-break: break-all;
}

button {
  margin-bottom: 20rpx;
}
</style>
