import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { MoneyCalculator } from '@/utils/money.js'

// 订单管理 Pinia Store
export const useOrderStore = defineStore('order', () => {
  const orderList = ref([])
  const total = ref(0)
  const loading = ref(false)
  const searchParams = ref({})

  // 计算属性 - 订单统计
  const orderStats = computed(() => {
    const stats = {
      pending: 0,    // 待支付
      paid: 0,       // 已支付
      shipped: 0,    // 已发货
      completed: 0,  // 已完成
      cancelled: 0,  // 已取消
      refunded: 0,   // 已退款
      today: 0,      // 今日订单
      totalAmount: 0 // 总金额
    }

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    orderList.value.forEach(order => {
      // 统计各状态订单数量
      if (stats.hasOwnProperty(order.status)) {
        stats[order.status]++
      }

      // 统计今日订单
      const orderDate = new Date(order.createTime)
      if (orderDate >= today) {
        stats.today++
      }

      // 统计总金额（已支付和已完成的订单）
      // 注意：数据库中金额单位是分，这里累加分值
      if (order.status === 'paid' || order.status === 'completed') {
        stats.totalAmount += order.actualAmount || order.totalFee || 0
      }
    })

    return stats
  })

  // 获取订单列表（支持分页和搜索）
  async function fetchOrderList(params = {}) {
    loading.value = true
    try {
      const { result } = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'getList',
          pageNum: params.pageNum || 1,
          pageSize: params.pageSize || 20,
          userId: params.userId,
          status: params.status,
          orderNo: params.orderNo,
          startTime: params.startTime,
          endTime: params.endTime,
          sortField: params.sortField || 'createTime',
          sortOrder: params.sortOrder || 'desc'
        }
      })

      if (result.code === 0) {
        orderList.value = result.data.list || []
        total.value = result.data.total || 0
        searchParams.value = params
        console.log(`订单列表加载成功，共 ${total.value} 条`)
        return result.data
      } else {
        throw new Error(result.msg || '获取订单列表失败')
      }
    } catch (error) {
      console.error('获取订单列表失败:', error)
      uni.showToast({ 
        title: error.message || '加载失败', 
        icon: 'error' 
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取全部订单（自动分页，直到全部获取完）
  async function fetchAllOrders(params = {}) {
    loading.value = true
    try {
      const pageSize = params.pageSize || 50
      let pageNum = 1
      let finished = false
      const allOrders = []
      let totalRemote = 0

      while (!finished) {
        const { result } = await uniCloud.callFunction({
          name: 'createOrder',
          data: {
            action: 'getList',
            pageNum,
            pageSize,
            userId: params.userId,
            status: params.status,
            orderNo: params.orderNo,
            startTime: params.startTime,
            endTime: params.endTime,
            sortField: params.sortField || 'createTime',
            sortOrder: params.sortOrder || 'desc'
          }
        })

        if (result.code === 0) {
          const pageData = result.data.list || []
          totalRemote = result.data.total || 0
          allOrders.push(...pageData)

          if (allOrders.length >= totalRemote || pageData.length < pageSize) {
            finished = true
          } else {
            pageNum += 1
          }
        } else {
          throw new Error(result.msg || '获取订单列表失败')
        }
      }

      orderList.value = allOrders
      total.value = totalRemote
      searchParams.value = params
      console.log(`已获取全部订单，共 ${total.value} 条`)
      return { list: allOrders, total: totalRemote }
    } catch (error) {
      console.error('获取全部订单失败:', error)
      uni.showToast({ 
        title: error.message || '加载失败', 
        icon: 'error' 
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建订单
  async function createOrder(orderData) {
    try {
      const { result } = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'create',
          ...orderData
        }
      })

      if (result.code === 0) {
        uni.showToast({ 
          title: '订单创建成功', 
          icon: 'success' 
        })
        // 重新加载订单列表
        await fetchOrderList(searchParams.value)
        return result.data
      } else {
        throw new Error(result.msg || '创建订单失败')
      }
    } catch (error) {
      console.error('创建订单失败:', error)
      uni.showToast({ 
        title: error.message || '创建失败', 
        icon: 'error' 
      })
      throw error
    }
  }

  // 更新订单
  async function updateOrder(orderId, updateData, userId = null) {
    try {
      const { result } = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'update',
          orderId,
          updateData,
          userId
        }
      })

      if (result.code === 0) {
        uni.showToast({ 
          title: '订单更新成功', 
          icon: 'success' 
        })
        // 重新加载订单列表
        await fetchOrderList(searchParams.value)
        return result
      } else {
        throw new Error(result.msg || '更新订单失败')
      }
    } catch (error) {
      console.error('更新订单失败:', error)
      uni.showToast({ 
        title: error.message || '更新失败', 
        icon: 'error' 
      })
      throw error
    }
  }

  // 删除订单
  async function deleteOrder(orderId, userId = null) {
    try {
      const { result } = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'delete',
          orderId,
          userId
        }
      })

      if (result.code === 0) {
        uni.showToast({ 
          title: '订单删除成功', 
          icon: 'success' 
        })
        // 重新加载订单列表
        await fetchOrderList(searchParams.value)
        return result
      } else {
        throw new Error(result.msg || '删除订单失败')
      }
    } catch (error) {
      console.error('删除订单失败:', error)
      uni.showToast({ 
        title: error.message || '删除失败', 
        icon: 'error' 
      })
      throw error
    }
  }

  // 更新订单状态
  async function updateOrderStatus(orderId, status, remark = '', userId = null) {
    try {
      const { result } = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'updateStatus',
          orderId,
          status,
          remark,
          userId
        }
      })

      if (result.code === 0) {
        uni.showToast({ 
          title: '状态更新成功', 
          icon: 'success' 
        })
        // 重新加载订单列表
        await fetchOrderList(searchParams.value)
        return result
      } else {
        throw new Error(result.msg || '更新状态失败')
      }
    } catch (error) {
      console.error('更新订单状态失败:', error)
      uni.showToast({ 
        title: error.message || '更新失败', 
        icon: 'error' 
      })
      throw error
    }
  }

  // 获取单个订单
  async function getOrder(orderId, userId = null) {
    try {
      const { result } = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'get',
          orderId,
          userId
        }
      })

      if (result.code === 0) {
        return result.data
      } else {
        throw new Error(result.msg || '获取订单失败')
      }
    } catch (error) {
      console.error('获取订单失败:', error)
      throw error
    }
  }

  // 清空订单列表
  function clearOrders() {
    orderList.value = []
    total.value = 0
    searchParams.value = {}
  }

  // 重置搜索参数并重新加载
  async function resetAndReload() {
    searchParams.value = {}
    await fetchAllOrders()
  }

  return {
    // 状态
    orderList,
    total,
    loading,
    searchParams,
    
    // 计算属性
    orderStats,
    
    // 方法
    fetchOrderList,
    fetchAllOrders,
    createOrder,
    updateOrder,
    deleteOrder,
    updateOrderStatus,
    getOrder,
    clearOrders,
    resetAndReload
  }
})
