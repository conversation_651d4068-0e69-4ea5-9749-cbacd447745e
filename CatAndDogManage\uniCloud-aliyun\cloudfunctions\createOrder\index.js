'use strict';

const db = uniCloud.database();
const ordersCollection = db.collection('orders');

exports.main = async (event, context) => {
  const { action } = event;

  try {
    switch (action) {
      case 'create':
        return await createOrder(event);
      case 'update':
      case 'updateOrder':
        return await updateOrder(event);
      case 'delete':
        return await deleteOrder(event);
      case 'get':
      case 'queryOrder':
        return await getOrder(event);
      case 'getList':
        return await getOrderList(event);
      case 'getAllOrders':
        return await getAllOrders(event);
      case 'updateStatus':
        return await updateOrderStatus(event);
      default:
        // 兼容旧版本调用（无action参数时默认为创建订单）
        return await createOrder(event);
    }
  } catch (error) {
    return {
      code: -1,
      msg: '操作失败',
      error: error.message
    };
  }
};



// 创建订单
async function createOrder(event) {
  try {
    // 参数验证
    if (!event.userId) {
      return { code: -1, msg: '用户ID不能为空' };
    }
    if (!event.totalFee || event.totalFee <= 0) {
      return { code: -1, msg: '订单金额必须大于0' };
    }
    if (!event.goods || !Array.isArray(event.goods) || event.goods.length === 0) {
      return { code: -1, msg: '商品信息不能为空' };
    }

    // 1. 生成订单号
    const orderNo = 'ORDER_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);

    // 2. 设置订单状态为未支付
    const orderStatus = 'unpaid'; // 未支付状态

    // 3. 构建订单数据
    const orderData = {
      orderNo,
      userId: event.userId,
      totalFee: event.totalFee,
      goods: event.goods,
      address: event.address || {},
      status: orderStatus,
      remark: event.remark || '',
      createTime: new Date(),
      updateTime: new Date(),
      subject: event.subject || '',
      // 添加更多订单字段
      discountAmount: event.discountAmount || 0, // 优惠金额
      shippingFee: event.shippingFee || 0, // 运费
      actualAmount: event.totalFee - (event.discountAmount || 0) + (event.shippingFee || 0), // 实际支付金额
    };

    // 4. 保存订单到数据库
    const result = await ordersCollection.add(orderData);

    // 5. 返回订单创建结果
    return {
      code: 0,
      msg: '订单创建成功',
      data: {
        orderNo,
        orderId: result.id,
        actualAmount: orderData.actualAmount,
        status: orderStatus
      }
    };
  } catch (error) {
    return {
      code: -1,
      msg: '创建订单失败',
      error: error.message
    };
  }
}

// 更新订单
async function updateOrder(event) {
  try {
    const { orderId, orderNo, updateData, userId } = event;

    // 参数验证
    if (!orderId && !orderNo) {
      return { code: -1, msg: '订单ID或订单号不能为空' };
    }
    if (!updateData || typeof updateData !== 'object') {
      return { code: -1, msg: '更新数据不能为空' };
    }

    // 构建查询条件
    let whereCondition = {};
    if (orderId) {
      whereCondition._id = orderId;
    } else {
      whereCondition.orderNo = orderNo;
    }

    // 如果提供了userId，添加用户验证（确保用户只能修改自己的订单）
    if (userId) {
      whereCondition.userId = userId;
    }

    // 添加更新时间
    updateData.updateTime = new Date();

    // 执行更新
    const result = await ordersCollection.where(whereCondition).update(updateData);

    if (result.updated === 0) {
      return { code: -1, msg: '订单不存在或无权限修改' };
    }

    return {
      code: 0,
      msg: '订单更新成功',
      data: { updated: result.updated }
    };
  } catch (error) {
    return {
      code: -1,
      msg: '更新订单失败',
      error: error.message
    };
  }
}

// 删除订单
async function deleteOrder(event) {
  try {
    const { orderId, orderNo, userId } = event;

    // 参数验证
    if (!orderId && !orderNo) {
      return { code: -1, msg: '订单ID或订单号不能为空' };
    }

    // 构建查询条件
    let whereCondition = {};
    if (orderId) {
      whereCondition._id = orderId;
    } else {
      whereCondition.orderNo = orderNo;
    }

    // 如果提供了userId，添加用户验证（确保用户只能删除自己的订单）
    if (userId) {
      whereCondition.userId = userId;
    }

    // 先查询订单是否存在
    const orderInfo = await ordersCollection.where(whereCondition).get();
    if (orderInfo.data.length === 0) {
      return { code: -1, msg: '订单不存在或无权限删除' };
    }

    // 检查订单状态，某些状态下不允许删除
    const order = orderInfo.data[0];
    if (order.status === 'paid' || order.status === 'shipped' || order.status === 'completed') {
      return { code: -1, msg: '该状态下的订单不允许删除' };
    }

    // 执行删除
    const result = await ordersCollection.where(whereCondition).remove();

    return {
      code: 0,
      msg: '订单删除成功',
      data: { deleted: result.deleted }
    };
  } catch (error) {
    return {
      code: -1,
      msg: '删除订单失败',
      error: error.message
    };
  }
}

// 获取单个订单
async function getOrder(event) {
  try {
    const { orderId, orderNo, userId } = event;

    // 参数验证
    if (!orderId && !orderNo) {
      return { code: -1, msg: '订单ID或订单号不能为空' };
    }

    // 构建查询条件
    let whereCondition = {};
    if (orderId) {
      whereCondition._id = orderId;
    } else {
      whereCondition.orderNo = orderNo;
    }

    // 如果提供了userId，添加用户验证
    if (userId) {
      whereCondition.userId = userId;
    }

    // 查询订单
    const result = await ordersCollection.where(whereCondition).get();

    if (result.data.length === 0) {
      return { code: -1, msg: '订单不存在或无权限查看' };
    }

    return {
      code: 0,
      msg: '获取订单成功',
      data: result.data[0]
    };
  } catch (error) {
    return {
      code: -1,
      msg: '获取订单失败',
      error: error.message
    };
  }
}

// 获取订单列表
async function getOrderList(event) {
  try {
    const {
      userId,
      status,
      pageSize = 10,
      pageNum = 1,
      keyword,
      orderNo,
      startTime,
      endTime,
      sortField = 'createTime',
      sortOrder = 'desc'
    } = event;

    // 参数验证
    if (!userId) {
      return { code: -1, msg: '用户ID不能为空' };
    }

    console.log('获取订单列表，参数:', { userId, status, pageNum, pageSize, keyword });

    // 构建查询条件
    let whereCondition = {
      userId: userId  // 确保只查询当前用户的订单
    };

    // 订单状态过滤
    if (status && status !== 'all') {
      whereCondition.status = status;
    }

    // 关键词搜索（订单号、商品名称、客户名称）
    if (keyword) {
      const keywordRegex = new RegExp(keyword, 'i');
      // 使用 $or 操作符进行多字段搜索
      whereCondition = db.command.and([
        whereCondition,
        db.command.or([
          { orderNo: keywordRegex },
          { subject: keywordRegex },
          { 'goods.name': keywordRegex },
          { 'address.name': keywordRegex } // 添加客户名称搜索
        ])
      ]);
    }

    // 订单号精确查询（优先级高于关键词搜索）
    if (orderNo) {
      whereCondition.orderNo = new RegExp(orderNo, 'i');
    }

    // 时间范围过滤
    if (startTime || endTime) {
      whereCondition.createTime = {};
      if (startTime) {
        whereCondition.createTime.$ = whereCondition.createTime.$ || {};
        whereCondition.createTime.$.gte = new Date(startTime);
      }
      if (endTime) {
        whereCondition.createTime.$ = whereCondition.createTime.$ || {};
        whereCondition.createTime.$.lte = new Date(endTime);
      }
    }

    // 查询订单列表
    const result = await ordersCollection
      .where(whereCondition)
      .orderBy(sortField, sortOrder)
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .get();

    // 获取总数
    const countResult = await ordersCollection.where(whereCondition).count();

    // 处理订单数据，格式化为前端需要的格式
    const orders = result.data.map(order => {
      return {
        id: order.orderNo || order._id,
        orderNo: order.orderNo,
        status: order.status,
        createTime: formatTime(order.createTime),
        payTime: order.payTime ? formatTime(order.payTime) : null,
        shipTime: order.shipTime ? formatTime(order.shipTime) : null,
        receiveTime: order.receiveTime ? formatTime(order.receiveTime) : null,
        completeTime: order.completeTime ? formatTime(order.completeTime) : null,
        cancelTime: order.cancelTime ? formatTime(order.cancelTime) : null,
        refundTime: order.refundTime ? formatTime(order.refundTime) : null,
        goods: order.goods || [],
        address: order.address || null,
        totalAmount: order.totalFee || 0, // 保持原始金额单位
        subject: order.subject || '',
        remark: order.remark || '',
        _id: order._id
      };
    });

    console.log('查询结果:', {
      total: countResult.total,
      current: orders.length,
      pageNum,
      pageSize
    });

    return {
      code: 200,
      msg: '获取订单列表成功',
      data: {
        orders: orders,
        pagination: {
          page: pageNum,
          limit: pageSize,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / pageSize),
          hasMore: pageNum * pageSize < countResult.total
        }
      }
    };
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return {
      code: -1,
      msg: '获取订单列表失败',
      error: error.message
    };
  }
}

// 获取全部订单（管理员用）
async function getAllOrders(event) {
  try {
    const {
      status,
      pageSize = 10,
      pageNum = 1,
      keyword,
      orderNo,
      startTime,
      endTime,
      sortField = 'createTime',
      sortOrder = 'desc'
    } = event;

    console.log('获取全部订单，参数:', { status, pageNum, pageSize, keyword });

    // 构建查询条件（不限制用户ID）
    let whereCondition = {};

    // 订单状态过滤
    if (status && status !== 'all') {
      whereCondition.status = status;
    }

    // 关键词搜索（订单号、商品名称、客户名称）
    if (keyword) {
      const keywordRegex = new RegExp(keyword, 'i');
      // 使用 $or 操作符进行多字段搜索
      whereCondition = db.command.and([
        whereCondition,
        db.command.or([
          { orderNo: keywordRegex },
          { subject: keywordRegex },
          { 'goods.name': keywordRegex },
          { 'address.name': keywordRegex } // 添加客户名称搜索
        ])
      ]);
    }

    // 订单号精确查询（优先级高于关键词搜索）
    if (orderNo) {
      whereCondition.orderNo = new RegExp(orderNo, 'i');
    }

    // 时间范围过滤
    if (startTime || endTime) {
      whereCondition.createTime = {};
      if (startTime) {
        whereCondition.createTime.$ = whereCondition.createTime.$ || {};
        whereCondition.createTime.$.gte = new Date(startTime);
      }
      if (endTime) {
        whereCondition.createTime.$ = whereCondition.createTime.$ || {};
        whereCondition.createTime.$.lte = new Date(endTime);
      }
    }

    // 查询订单列表
    const result = await ordersCollection
      .where(whereCondition)
      .orderBy(sortField, sortOrder)
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .get();

    // 获取总数
    const countResult = await ordersCollection.where(whereCondition).count();

    // 处理订单数据，格式化为前端需要的格式
    const orders = result.data.map(order => {
      return {
        id: order.orderNo || order._id,
        orderNo: order.orderNo,
        userId: order.userId, // 保留用户ID信息
        status: order.status,
        createTime: formatTime(order.createTime),
        payTime: order.payTime ? formatTime(order.payTime) : null,
        shipTime: order.shipTime ? formatTime(order.shipTime) : null,
        receiveTime: order.receiveTime ? formatTime(order.receiveTime) : null,
        completeTime: order.completeTime ? formatTime(order.completeTime) : null,
        cancelTime: order.cancelTime ? formatTime(order.cancelTime) : null,
        refundTime: order.refundTime ? formatTime(order.refundTime) : null,
        goods: order.goods || [],
        address: order.address || null,
        totalAmount: order.totalFee || 0, // 保持原始金额单位
        discountAmount: order.discountAmount || 0, // 保持原始金额单位
        shippingFee: order.shippingFee || 0, // 保持原始金额单位
        actualAmount: order.actualAmount || order.totalFee || 0, // 保持原始金额单位
        subject: order.subject || '',
        remark: order.remark || '',
        _id: order._id
      };
    });

    console.log('查询结果:', {
      total: countResult.total,
      current: orders.length,
      pageNum,
      pageSize
    });

    return {
      code: 200,
      msg: '获取全部订单成功',
      data: {
        list: orders, // 注意这里使用 list 而不是 orders，保持与前端一致
        pagination: {
          page: pageNum,
          limit: pageSize,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / pageSize),
          hasMore: pageNum * pageSize < countResult.total
        }
      }
    };
  } catch (error) {
    console.error('获取全部订单失败:', error);
    return {
      code: -1,
      msg: '获取全部订单失败',
      error: error.message
    };
  }
}

// 更新订单状态
async function updateOrderStatus(event) {
  try {
    const { orderId, orderNo, status, userId, remark } = event;

    // 参数验证
    if (!orderId && !orderNo) {
      return { code: -1, msg: '订单ID或订单号不能为空' };
    }
    if (!status) {
      return { code: -1, msg: '订单状态不能为空' };
    }

    // 验证状态值 - 新的订单状态系统
    const validStatuses = [
      'unpaid',           // 未支付
      'paid',             // 已支付
      'pending-receipt',  // 待收货
      'received',         // 已收货
      'pending-return',   // 待退货
      'returned',         // 已退货
      'completed',        // 已完成
      'cancelled'         // 已取消
    ];
    if (!validStatuses.includes(status)) {
      return { code: -1, msg: '无效的订单状态' };
    }

    // 构建查询条件
    let whereCondition = {};
    if (orderId) {
      whereCondition._id = orderId;
    } else {
      whereCondition.orderNo = orderNo;
    }

    // 如果提供了userId，添加用户验证
    if (userId) {
      whereCondition.userId = userId;
    }

    // 先查询当前订单状态
    const orderInfo = await ordersCollection.where(whereCondition).get();
    if (orderInfo.data.length === 0) {
      return { code: -1, msg: '订单不存在或无权限修改' };
    }

    const currentOrder = orderInfo.data[0];

    // 状态流转验证 - 新的订单状态系统
    const statusFlow = {
      'unpaid': ['paid', 'cancelled'],                    // 未支付 → 已支付/已取消
      'paid': ['pending-receipt', 'cancelled'],           // 已支付 → 待收货/已取消
      'pending-receipt': ['received', 'cancelled'],       // 待收货 → 已收货/已取消
      'received': ['completed', 'pending-return'],        // 已收货 → 已完成/待退货
      'pending-return': ['returned', 'received'],         // 待退货 → 已退货/已收货
      'returned': [],                                     // 已退货 → 无后续状态
      'completed': ['pending-return'],                    // 已完成 → 待退货
      'cancelled': []                                     // 已取消 → 无后续状态
    };

    if (!statusFlow[currentOrder.status].includes(status)) {
      return {
        code: -1,
        msg: `订单状态不能从 ${currentOrder.status} 变更为 ${status}`
      };
    }

    // 构建更新数据
    const updateData = {
      status,
      updateTime: new Date()
    };

    // 根据状态添加相应的时间戳 - 新的订单状态系统
    switch (status) {
      case 'paid':
        updateData.payTime = new Date();
        break;
      case 'pending-receipt':
        updateData.shipTime = new Date();
        break;
      case 'received':
        updateData.receiveTime = new Date();
        break;
      case 'completed':
        updateData.completeTime = new Date();
        break;
      case 'cancelled':
        updateData.cancelTime = new Date();
        break;
      case 'pending-return':
        updateData.returnApplyTime = new Date();
        break;
      case 'returned':
        updateData.returnTime = new Date();
        break;
    }

    // 添加备注
    if (remark) {
      updateData.statusRemark = remark;
    }

    // 执行更新
    const result = await ordersCollection.where(whereCondition).update(updateData);

    return {
      code: 0,
      msg: '订单状态更新成功',
      data: {
        updated: result.updated,
        oldStatus: currentOrder.status,
        newStatus: status
      }
    };
  } catch (error) {
    return {
      code: -1,
      msg: '更新订单状态失败',
      error: error.message
    };
  }
}

/**
 * 格式化时间
 * @param {Date|string} time - 时间对象或字符串
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(time) {
  if (!time) return null;

  const date = new Date(time);
  if (isNaN(date.getTime())) return null;

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}