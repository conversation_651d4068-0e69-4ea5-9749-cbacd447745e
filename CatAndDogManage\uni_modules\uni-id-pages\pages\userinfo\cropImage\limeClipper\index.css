.flex-auto {
  flex: auto;
}
.bg-transparent {
  background-color: rgba(0,0,0,0.9);
  transition-duration: 0.35s;
}
.l-clipper {
  width: 100vw;
  height: calc(100vh - var(--window-top));
  background-color: rgba(0,0,0,0.9);
  position: fixed;
  top: var(--window-top);
  left: 0;
  z-index: 1;
}
.l-clipper-mask {
  position: relative;
  z-index: 2;
  pointer-events: none;
}
.l-clipper__content {
  pointer-events: none;
  position: absolute;
  border: 1rpx solid rgba(255,255,255,0.3);
  box-sizing: border-box;
  box-shadow: rgba(0,0,0,0.5) 0 0 0 80vh;
  background: transparent;
}
.l-clipper__content::before,
.l-clipper__content::after {
  content: '';
  position: absolute;
  border: 1rpx dashed rgba(255,255,255,0.3);
}
.l-clipper__content::before {
  width: 100%;
  top: 33.33%;
  height: 33.33%;
  border-left: none;
  border-right: none;
}
.l-clipper__content::after {
  width: 33.33%;
  left: 33.33%;
  height: 100%;
  border-top: none;
  border-bottom: none;
}
.l-clipper__edge {
  position: absolute;
  width: 34rpx;
  height: 34rpx;
  border: 6rpx solid #fff;
  pointer-events: auto;
}
.l-clipper__edge::before {
  content: '';
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  background-color: transparent;
}
.l-clipper__edge:nth-child(1) {
  left: -6rpx;
  top: -6rpx;
  border-bottom-width: 0 !important;
  border-right-width: 0 !important;
}
.l-clipper__edge:nth-child(1):before {
  top: -50%;
  left: -50%;
}
.l-clipper__edge:nth-child(2) {
  right: -6rpx;
  top: -6rpx;
  border-bottom-width: 0 !important;
  border-left-width: 0 !important;
}
.l-clipper__edge:nth-child(2):before {
  top: -50%;
  left: 50%;
}
.l-clipper__edge:nth-child(3) {
  left: -6rpx;
  bottom: -6rpx;
  border-top-width: 0 !important;
  border-right-width: 0 !important;
}
.l-clipper__edge:nth-child(3):before {
  bottom: -50%;
  left: -50%;
}
.l-clipper__edge:nth-child(4) {
  right: -6rpx;
  bottom: -6rpx;
  border-top-width: 0 !important;
  border-left-width: 0 !important;
}
.l-clipper__edge:nth-child(4):before {
  bottom: -50%;
  left: 50%;
}
.l-clipper-image {
  width: 100%;
  border-style: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform-origin: center;
}
.l-clipper-canvas {
  position: fixed;
  z-index: 10;
  left: -200vw;
  top: -200vw;
  pointer-events: none;
}
.l-clipper-tools {
  position: fixed;
  left: 0;
  bottom: 10px;
  width: 100%;
  z-index: 99;
  color: #fff;
}
.l-clipper-tools__btns {
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 20rpx 40rpx;
  box-sizing: border-box;
}
.l-clipper-tools__btns .cancel {
  width: 112rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
}
.l-clipper-tools__btns .confirm {
  width: 112rpx;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #07c160;
  border-radius: 6rpx;
  text-align: center;
}
.l-clipper-tools__btns image {
  display: block;
  width: 60rpx;
  height: 60rpx;
}
.l-clipper-tools__btns {
  flex-direction: row;
}
