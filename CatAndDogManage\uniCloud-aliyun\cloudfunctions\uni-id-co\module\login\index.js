module.exports = {
  login: require('./login'),
  loginBySms: require('./login-by-sms'),
  loginByUniverify: require('./login-by-univerify'),
  loginByWeixin: require('./login-by-weixin'),
  loginByAlipay: require('./login-by-alipay'),
  loginByQQ: require('./login-by-qq'),
  loginByApple: require('./login-by-apple'),
  loginByBaidu: require('./login-by-baidu'),
  loginByDingtalk: require('./login-by-dingtalk'),
  loginByToutiao: require('./login-by-toutiao'),
  loginByDouyin: require('./login-by-douyin'),
  loginByWeibo: require('./login-by-weibo'),
  loginByTaobao: require('./login-by-taobao'),
  loginByEmailLink: require('./login-by-email-link'),
  loginByEmailCode: require('./login-by-email-code'),
  loginByFacebook: require('./login-by-facebook'),
  loginByGoogle: require('./login-by-google'),
  loginByWeixinMobile: require('./login-by-weixin-mobile')
}
