/**
 * 金额处理工具函数
 * 数据库存储单位：分
 * 显示单位：元
 */

/**
 * 分转元 - 用于显示
 * @param {number} cents 分
 * @returns {string} 元（保留2位小数）
 */
export function centsToYuan(cents) {
  if (cents === null || cents === undefined || isNaN(cents)) {
    return '0.00'
  }
  return (cents / 100).toFixed(2)
}

/**
 * 元转分 - 用于存储
 * @param {number} yuan 元
 * @returns {number} 分
 */
export function yuanToCents(yuan) {
  if (yuan === null || yuan === undefined || isNaN(yuan)) {
    return 0
  }
  return Math.round(yuan * 100)
}

/**
 * 格式化金额显示
 * @param {number} cents 分
 * @param {string} symbol 货币符号，默认 ¥
 * @returns {string} 格式化后的金额字符串
 */
export function formatMoney(cents, symbol = '¥') {
  return `${symbol}${centsToYuan(cents)}`
}

/**
 * 格式化金额显示（输入已经是元）
 * @param {number} yuan 元
 * @param {string} symbol 货币符号，默认 ¥
 * @returns {string} 格式化后的金额字符串
 */
export function formatMoneyYuan(yuan, symbol = '¥') {
  if (yuan === null || yuan === undefined || isNaN(yuan)) {
    return `${symbol}0.00`
  }
  return `${symbol}${Number(yuan).toFixed(2)}`
}

/**
 * 格式化金额显示（带千分位分隔符）
 * @param {number} cents 分
 * @param {string} symbol 货币符号，默认 ¥
 * @returns {string} 格式化后的金额字符串
 */
export function formatMoneyWithComma(cents, symbol = '¥') {
  const yuan = centsToYuan(cents)
  const parts = yuan.split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return `${symbol}${parts.join('.')}`
}

/**
 * 解析用户输入的金额（元）为分
 * @param {string|number} input 用户输入的金额
 * @returns {number} 分
 */
export function parseMoneyInput(input) {
  if (!input) return 0
  
  // 移除货币符号和空格
  const cleanInput = String(input).replace(/[¥$€£\s,]/g, '')
  const yuan = parseFloat(cleanInput)
  
  if (isNaN(yuan)) return 0
  
  return yuanToCents(yuan)
}

/**
 * 验证金额输入是否有效
 * @param {string|number} input 用户输入的金额
 * @returns {boolean} 是否有效
 */
export function isValidMoneyInput(input) {
  if (!input) return false
  
  const cleanInput = String(input).replace(/[¥$€£\s,]/g, '')
  const yuan = parseFloat(cleanInput)
  
  return !isNaN(yuan) && yuan >= 0 && yuan <= 999999999.99
}

/**
 * 计算订单实际金额（分）
 * @param {number} totalFee 订单总金额（分）
 * @param {number} discountAmount 优惠金额（分）
 * @param {number} shippingFee 运费（分）
 * @returns {number} 实际支付金额（分）
 */
export function calculateActualAmount(totalFee = 0, discountAmount = 0, shippingFee = 0) {
  return Math.max(0, totalFee - discountAmount + shippingFee)
}

/**
 * 金额统计工具
 */
export class MoneyCalculator {
  constructor() {
    this.total = 0
  }
  
  /**
   * 添加金额
   * @param {number} cents 分
   */
  add(cents) {
    this.total += cents || 0
    return this
  }
  
  /**
   * 减去金额
   * @param {number} cents 分
   */
  subtract(cents) {
    this.total -= cents || 0
    return this
  }
  
  /**
   * 获取总额（分）
   * @returns {number} 分
   */
  getTotalCents() {
    return this.total
  }
  
  /**
   * 获取总额（元）
   * @returns {string} 元
   */
  getTotalYuan() {
    return centsToYuan(this.total)
  }
  
  /**
   * 格式化显示总额
   * @param {string} symbol 货币符号
   * @returns {string} 格式化金额
   */
  format(symbol = '¥') {
    return formatMoney(this.total, symbol)
  }
  
  /**
   * 重置
   */
  reset() {
    this.total = 0
    return this
  }
}

export default {
  centsToYuan,
  yuanToCents,
  formatMoney,
  formatMoneyYuan,
  formatMoneyWithComma,
  parseMoneyInput,
  isValidMoneyInput,
  calculateActualAmount,
  MoneyCalculator
}
