/**
 * 订单服务 - 封装订单相关的云函数调用
 */

class OrderService {
  
  /**
   * 创建订单
   * @param {Object} orderData 订单数据
   * @returns {Promise} 
   */
  static async createOrder(orderData) {
    try {
      const result = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'create',
          ...orderData
        }
      });
      
      if (result.result.code === 0) {
        uni.showToast({
          title: '订单创建成功',
          icon: 'success'
        });
        return result.result;
      } else {
        uni.showToast({
          title: result.result.msg || '创建失败',
          icon: 'error'
        });
        throw new Error(result.result.msg);
      }
    } catch (error) {
      console.error('创建订单失败:', error);
      uni.showToast({
        title: '网络错误',
        icon: 'error'
      });
      throw error;
    }
  }

  /**
   * 更新订单
   * @param {String} orderId 订单ID
   * @param {Object} updateData 更新数据
   * @param {String} userId 用户ID（可选）
   * @returns {Promise}
   */
  static async updateOrder(orderId, updateData, userId = null) {
    try {
      const result = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'update',
          orderId,
          updateData,
          userId
        }
      });
      
      if (result.result.code === 0) {
        uni.showToast({
          title: '订单更新成功',
          icon: 'success'
        });
        return result.result;
      } else {
        uni.showToast({
          title: result.result.msg || '更新失败',
          icon: 'error'
        });
        throw new Error(result.result.msg);
      }
    } catch (error) {
      console.error('更新订单失败:', error);
      throw error;
    }
  }

  /**
   * 删除订单
   * @param {String} orderId 订单ID
   * @param {String} userId 用户ID（可选）
   * @returns {Promise}
   */
  static async deleteOrder(orderId, userId = null) {
    try {
      const result = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'delete',
          orderId,
          userId
        }
      });
      
      if (result.result.code === 0) {
        uni.showToast({
          title: '订单删除成功',
          icon: 'success'
        });
        return result.result;
      } else {
        uni.showToast({
          title: result.result.msg || '删除失败',
          icon: 'error'
        });
        throw new Error(result.result.msg);
      }
    } catch (error) {
      console.error('删除订单失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个订单
   * @param {String} orderId 订单ID
   * @param {String} userId 用户ID（可选）
   * @returns {Promise}
   */
  static async getOrder(orderId, userId = null) {
    try {
      const result = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'get',
          orderId,
          userId
        }
      });
      
      if (result.result.code === 0) {
        return result.result.data;
      } else {
        throw new Error(result.result.msg);
      }
    } catch (error) {
      console.error('获取订单失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static async getOrderList(params = {}) {
    try {
      const result = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'getList',
          ...params
        }
      });
      
      if (result.result.code === 0) {
        return result.result.data;
      } else {
        throw new Error(result.result.msg);
      }
    } catch (error) {
      console.error('获取订单列表失败:', error);
      throw error;
    }
  }

  /**
   * 更新订单状态
   * @param {String} orderId 订单ID
   * @param {String} status 新状态
   * @param {String} remark 备注（可选）
   * @param {String} userId 用户ID（可选）
   * @returns {Promise}
   */
  static async updateOrderStatus(orderId, status, remark = '', userId = null) {
    try {
      const result = await uniCloud.callFunction({
        name: 'createOrder',
        data: {
          action: 'updateStatus',
          orderId,
          status,
          remark,
          userId
        }
      });
      
      if (result.result.code === 0) {
        uni.showToast({
          title: '状态更新成功',
          icon: 'success'
        });
        return result.result;
      } else {
        uni.showToast({
          title: result.result.msg || '更新失败',
          icon: 'error'
        });
        throw new Error(result.result.msg);
      }
    } catch (error) {
      console.error('更新订单状态失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新订单状态
   * @param {Array} orderIds 订单ID数组
   * @param {String} status 新状态
   * @param {String} remark 备注（可选）
   * @returns {Promise}
   */
  static async batchUpdateOrderStatus(orderIds, status, remark = '') {
    try {
      const promises = orderIds.map(orderId => 
        this.updateOrderStatus(orderId, status, remark)
      );
      
      const results = await Promise.allSettled(promises);
      
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failCount = results.filter(r => r.status === 'rejected').length;
      
      if (failCount === 0) {
        uni.showToast({
          title: `批量更新成功(${successCount}条)`,
          icon: 'success'
        });
      } else {
        uni.showToast({
          title: `部分更新失败(成功${successCount}条，失败${failCount}条)`,
          icon: 'none'
        });
      }
      
      return { successCount, failCount, results };
    } catch (error) {
      console.error('批量更新订单状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单统计数据
   * @param {String} userId 用户ID（可选）
   * @returns {Promise}
   */
  static async getOrderStats(userId = null) {
    try {
      // 获取各状态订单数量
      const statusList = ['pending', 'paid', 'shipped', 'completed', 'cancelled', 'refunded'];
      const promises = statusList.map(status => 
        this.getOrderList({ userId, status, pageSize: 1 })
      );
      
      const results = await Promise.all(promises);
      
      const stats = {};
      statusList.forEach((status, index) => {
        stats[status] = results[index].total || 0;
      });
      
      // 计算今日订单
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayResult = await this.getOrderList({
        userId,
        startTime: today.toISOString(),
        pageSize: 1
      });
      stats.today = todayResult.total || 0;
      
      return stats;
    } catch (error) {
      console.error('获取订单统计失败:', error);
      throw error;
    }
  }
}

export default OrderService;
