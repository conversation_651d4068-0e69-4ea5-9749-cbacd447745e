# createOrder 云函数使用说明

## 功能概述

`createOrder` 云函数提供完整的订单管理功能，包括：
- 创建订单
- 更新订单
- 删除订单
- 获取单个订单
- 获取订单列表
- 更新订单状态

## API 接口

### 1. 创建订单

```javascript
// 调用方式
const result = await uniCloud.callFunction({
  name: 'createOrder',
  data: {
    action: 'create', // 可选，不传默认为创建
    userId: 'user123',
    totalFee: 299.00,
    goods: [
      {
        goodsId: 'goods001',
        name: '商品名称',
        price: 99.00,
        quantity: 2,
        image: 'https://example.com/image.jpg'
      }
    ],
    address: {
      name: '张三',
      phone: '13800138000',
      province: '广东省',
      city: '深圳市',
      district: '南山区',
      detail: '科技园南区'
    },
    paymentMethod: 'wechat',
    remark: '订单备注',
    subject: '订单标题',
    discountAmount: 10.00, // 优惠金额
    shippingFee: 10.00,    // 运费
    isMock: false // 是否模拟支付
  }
});

// 返回结果
{
  code: 0,
  msg: '订单已创建，待支付',
  data: {
    orderNo: 'ORDER_1640995200000_abc123',
    orderId: '61c8f123456789abcdef0001',
    actualAmount: 299.00
  }
}
```

### 2. 更新订单

```javascript
const result = await uniCloud.callFunction({
  name: 'createOrder',
  data: {
    action: 'update',
    orderId: '61c8f123456789abcdef0001', // 或使用 orderNo
    userId: 'user123', // 可选，用于权限验证
    updateData: {
      remark: '更新后的备注',
      address: {
        name: '李四',
        phone: '13900139000'
      }
    }
  }
});
```

### 3. 删除订单

```javascript
const result = await uniCloud.callFunction({
  name: 'createOrder',
  data: {
    action: 'delete',
    orderId: '61c8f123456789abcdef0001',
    userId: 'user123' // 可选，用于权限验证
  }
});
```

### 4. 获取单个订单

```javascript
const result = await uniCloud.callFunction({
  name: 'createOrder',
  data: {
    action: 'get',
    orderId: '61c8f123456789abcdef0001', // 或使用 orderNo
    userId: 'user123' // 可选，用于权限验证
  }
});

// 返回结果
{
  code: 0,
  msg: '获取订单成功',
  data: {
    _id: '61c8f123456789abcdef0001',
    orderNo: 'ORDER_1640995200000_abc123',
    userId: 'user123',
    totalFee: 299.00,
    status: 'pending',
    goods: [...],
    address: {...},
    createTime: '2021-12-31T16:00:00.000Z',
    updateTime: '2021-12-31T16:00:00.000Z'
  }
}
```

### 5. 获取订单列表

```javascript
const result = await uniCloud.callFunction({
  name: 'createOrder',
  data: {
    action: 'getList',
    userId: 'user123',     // 可选，按用户过滤
    status: 'pending',     // 可选，按状态过滤
    orderNo: 'ORDER_',     // 可选，订单号模糊查询
    startTime: '2021-12-01', // 可选，开始时间
    endTime: '2021-12-31',   // 可选，结束时间
    pageSize: 20,          // 可选，每页数量，默认20
    pageNum: 1,            // 可选，页码，默认1
    sortField: 'createTime', // 可选，排序字段，默认createTime
    sortOrder: 'desc'      // 可选，排序方向，默认desc
  }
});

// 返回结果
{
  code: 0,
  msg: '获取订单列表成功',
  data: {
    list: [...],
    total: 100,
    pageNum: 1,
    pageSize: 20,
    totalPages: 5
  }
}
```

### 6. 更新订单状态

```javascript
const result = await uniCloud.callFunction({
  name: 'createOrder',
  data: {
    action: 'updateStatus',
    orderId: '61c8f123456789abcdef0001',
    status: 'paid',
    userId: 'user123', // 可选，用于权限验证
    remark: '支付成功'  // 可选，状态变更备注
  }
});
```

## 订单状态说明

| 状态 | 说明 | 可转换状态 |
|------|------|-----------|
| pending | 待支付 | paid, cancelled |
| paid | 已支付 | shipped, refunded |
| shipped | 已发货 | completed |
| completed | 已完成 | refunded |
| cancelled | 已取消 | - |
| refunded | 已退款 | - |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| -1 | 失败 |
| -99 | 未知操作 |

## 注意事项

1. **权限控制**: 建议在实际使用中添加更严格的权限验证
2. **数据验证**: 对输入参数进行更详细的验证
3. **事务处理**: 对于涉及多表操作的场景，建议使用数据库事务
4. **日志记录**: 建议添加操作日志记录功能
5. **支付集成**: 真实支付需要集成 uni-pay 等支付插件

## 数据库表结构

订单表 `orders` 主要字段：

```javascript
{
  _id: ObjectId,           // 订单ID
  orderNo: String,         // 订单号
  userId: String,          // 用户ID
  totalFee: Number,        // 订单总金额
  discountAmount: Number,  // 优惠金额
  shippingFee: Number,     // 运费
  actualAmount: Number,    // 实际支付金额
  goods: Array,            // 商品信息
  address: Object,         // 收货地址
  status: String,          // 订单状态
  paymentMethod: String,   // 支付方式
  remark: String,          // 订单备注
  subject: String,         // 订单标题
  createTime: Date,        // 创建时间
  updateTime: Date,        // 更新时间
  payTime: Date,           // 支付时间
  shipTime: Date,          // 发货时间
  completeTime: Date,      // 完成时间
  cancelTime: Date,        // 取消时间
  refundTime: Date,        // 退款时间
  statusRemark: String     // 状态变更备注
}
```
