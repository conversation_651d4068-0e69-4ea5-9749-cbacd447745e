<template>
  <view class="demo-page">
    <view class="page-header">
      <text class="page-title">订单管理演示</text>
      <text class="page-desc">演示通过 Store 调用云函数获取订单数据</text>
    </view>
    
    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-title">订单统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-label">总订单数</text>
          <text class="stat-value">{{ total }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">待支付</text>
          <text class="stat-value">{{ orderStats.pending }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">已支付</text>
          <text class="stat-value">{{ orderStats.paid }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">今日订单</text>
          <text class="stat-value">{{ orderStats.today }}</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-section">
      <button type="primary" @click="loadAllOrders" :loading="loading">
        {{ loading ? '加载中...' : '获取全部订单' }}
      </button>
      <button type="default" @click="createTestOrder" :loading="creating">
        {{ creating ? '创建中...' : '创建测试订单' }}
      </button>
      <button type="warn" @click="clearOrders">清空订单列表</button>
    </view>
    
    <!-- 订单列表 -->
    <view class="order-section">
      <view class="section-title">订单列表 (共 {{ orderList.length }} 条)</view>
      
      <view v-if="orderList.length === 0" class="empty-state">
        <text>暂无订单数据</text>
        <text>点击"获取全部订单"按钮加载数据</text>
      </view>
      
      <view v-else class="order-list">
        <view v-for="order in displayOrders" :key="order._id" class="order-item">
          <view class="order-header">
            <text class="order-no">{{ order.orderNo }}</text>
            <text class="order-status" :class="'status-' + order.status">
              {{ getStatusText(order.status) }}
            </text>
          </view>
          <view class="order-info">
            <text>客户：{{ order.address?.name || '-' }}</text>
            <text>金额：{{ formatMoney(order.actualAmount || order.totalFee) }}</text>
            <text>时间：{{ formatDate(order.createTime) }}</text>
          </view>
          <view class="order-actions">
            <button size="mini" @click="viewOrderDetail(order)">详情</button>
            <button size="mini" type="warn" @click="deleteOrderItem(order._id)">删除</button>
          </view>
        </view>
      </view>
      
      <!-- 分页 -->
      <view v-if="orderList.length > pageSize" class="pagination">
        <button :disabled="currentPage === 1" @click="currentPage--">上一页</button>
        <text>{{ currentPage }} / {{ totalPages }}</text>
        <button :disabled="currentPage === totalPages" @click="currentPage++">下一页</button>
      </view>
    </view>
    
    <!-- 订单详情弹窗 -->
    <uni-popup ref="detailPopup" type="center">
      <view class="order-detail-popup">
        <view v-if="selectedOrder" class="detail-container">
          <view class="detail-header">
            <text class="detail-title">订单详情</text>
            <uni-icons type="close" size="24" color="#666" @click="$refs.detailPopup.close()" />
          </view>
          <scroll-view scroll-y class="detail-content">
            <view class="detail-section">
              <view class="section-title">基本信息</view>
              <view class="detail-item">
                <text class="label">订单号</text>
                <text class="value">{{ selectedOrder.orderNo }}</text>
              </view>
              <view class="detail-item">
                <text class="label">下单用户ID</text>
                <text class="value">{{ selectedOrder.userId }}</text>
              </view>
              <view class="detail-item">
                <text class="label">订单状态</text>
                <text class="value status" :class="'status-' + selectedOrder.status">{{ getStatusText(selectedOrder.status) }}</text>
              </view>
              <view class="detail-item">
                <text class="label">创建时间</text>
                <text class="value">{{ formatDate(selectedOrder.createTime) }}</text>
              </view>
            </view>

            <view class="detail-section">
              <view class="section-title">收货信息</view>
              <view class="detail-item">
                <text class="label">收货人</text>
                <text class="value">{{ selectedOrder.address?.name || '-' }}</text>
              </view>
              <view class="detail-item">
                <text class="label">联系电话</text>
                <text class="value">{{ selectedOrder.address?.phone || '-' }}</text>
              </view>
              <view class="detail-item">
                <text class="label">收货地址</text>
                <text class="value">{{ formatAddress(selectedOrder.address) }}</text>
              </view>
            </view>

            <view class="detail-section">
              <view class="section-title">商品信息</view>
              <view v-for="(item, index) in selectedOrder.goods" :key="index" class="goods-item">
                <image :src="item.cover_image" class="goods-image" mode="aspectFill" />
                <view class="goods-info">
                  <view class="goods-name">{{ item.name }}</view>
                  <view class="goods-category">{{ item.category }}</view>
                  <view class="goods-brief">{{ item.brief }}</view>
                  <view class="goods-price-row">
                    <text class="goods-price">{{ formatMoney(item.price) }}</text>
                    <text class="goods-quantity">x{{ item.num }}</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="detail-section">
              <view class="section-title">金额信息</view>
              <view class="amount-row">
                <text class="amount-label">订单总额</text>
                <text class="amount-value">{{ formatMoney(selectedOrder.totalFee) }}</text>
              </view>
              <view class="amount-row">
                <text class="amount-label">优惠金额</text>
                <text class="amount-value">{{ formatMoney(selectedOrder.discountAmount || 0) }}</text>
              </view>
              <view class="amount-row">
                <text class="amount-label">运费</text>
                <text class="amount-value">{{ formatMoney(selectedOrder.shippingFee || 0) }}</text>
              </view>
              <view class="amount-row total-row">
                <text class="amount-label">实付金额</text>
                <text class="amount-value total">{{ formatMoney(selectedOrder.actualAmount || selectedOrder.totalFee) }}</text>
              </view>
            </view>

            <view v-if="selectedOrder.remark" class="detail-section">
              <view class="section-title">备注信息</view>
              <view class="remark-content">{{ selectedOrder.remark }}</view>
            </view>
          </scroll-view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { useOrderStore } from '@/store/modules/order.js'
import { storeToRefs } from 'pinia'
import { formatMoney, yuanToCents, centsToYuan } from '@/utils/money.js'

export default {
  setup() {
    const orderStore = useOrderStore()
    const { orderList, total, loading, orderStats } = storeToRefs(orderStore)
    const { fetchAllOrders, createOrder, deleteOrder, clearOrders } = orderStore

    return {
      orderStore,
      orderList,
      total,
      loading,
      orderStats,
      fetchAllOrders,
      createOrder,
      deleteOrder,
      clearOrders
    }
  },
  
  data() {
    return {
      creating: false,
      selectedOrder: null,
      currentPage: 1,
      pageSize: 5
    }
  },
  
  computed: {
    totalPages() {
      return Math.ceil(this.orderList.length / this.pageSize) || 1
    },
    displayOrders() {
      const start = (this.currentPage - 1) * this.pageSize
      return this.orderList.slice(start, start + this.pageSize)
    }
  },
  
  async mounted() {
    // 页面加载时自动获取全部订单
    await this.loadAllOrders()
  },
  
  methods: {
    // 加载全部订单
    async loadAllOrders() {
      try {
        await this.fetchAllOrders()
        uni.showToast({
          title: `加载成功，共${this.total}条订单`,
          icon: 'success'
        })
      } catch (error) {
        console.error('加载订单失败:', error)
      }
    },
    
    // 创建测试订单
    async createTestOrder() {
      this.creating = true
      try {
        const orderData = {
          userId: 'demo_user_' + Date.now(),
          // 将元转换为分存储到数据库
          totalFee: yuanToCents(Math.floor(Math.random() * 1000) + 100),
          goods: [
            {
              goodsId: 'demo_goods_' + Date.now(),
              name: '演示商品',
              price: yuanToCents(99.00), // 商品价格也转换为分
              quantity: 1,
              image: 'https://example.com/demo.jpg'
            }
          ],
          address: {
            name: '演示用户',
            phone: '13800138000',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            detail: '演示地址'
          },
          paymentMethod: 'wechat',
          remark: '这是一个演示订单',
          subject: '演示订单',
          isMock: true
        }
        
        await this.createOrder(orderData)
        this.currentPage = 1 // 回到第一页查看新订单
      } catch (error) {
        console.error('创建测试订单失败:', error)
      } finally {
        this.creating = false
      }
    },
    
    // 查看订单详情
    viewOrderDetail(order) {
      this.selectedOrder = order
      this.$refs.detailPopup.open()
    },
    
    // 删除订单
    async deleteOrderItem(orderId) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该订单吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await this.deleteOrder(orderId)
            } catch (error) {
              console.error('删除订单失败:', error)
            }
          }
        }
      })
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: '待支付',
        paid: '已支付',
        shipped: '已发货',
        completed: '已完成',
        cancelled: '已取消',
        refunded: '已退款'
      }
      return statusMap[status] || status
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    // 格式化地址
    formatAddress(address) {
      if (!address) return '-'
      const { province, city, district, detail } = address
      return `${province || ''}${city || ''}${district || ''}${detail || ''}`
    },

    // 格式化金额显示
    formatMoney(cents) {
      return formatMoney(cents)
    }
  }
}
</script>

<style scoped>
.demo-page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
}

.stats-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #2979ff;
}

.action-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-section button {
  flex: 1;
}

.order-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-state text {
  display: block;
  margin-bottom: 10rpx;
}

.order-item {
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.order-no {
  font-weight: bold;
  color: #333;
}

.order-status {
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
  color: #fff;
}

.status-pending { background: #ff9800; }
.status-paid { background: #4caf50; }
.status-shipped { background: #2196f3; }
.status-completed { background: #8bc34a; }
.status-cancelled { background: #f44336; }
.status-refunded { background: #9c27b0; }

.order-info {
  margin-bottom: 15rpx;
}

.order-info text {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.order-actions {
  display: flex;
  gap: 10rpx;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
  margin-top: 30rpx;
}

/* 订单详情弹窗样式 */
.order-detail-popup {
  background: #fff;
  border-radius: 16rpx;
  width: 90vw;
  max-width: 800rpx;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.detail-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.detail-content {
  flex: 1;
  padding: 0 40rpx;
  max-height: 60vh;
}

.detail-section {
  margin: 30rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2px solid #2979ff;
  display: inline-block;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.detail-item .label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.detail-item .value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.value.status {
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  color: #fff;
  font-size: 24rpx;
}

.status-pending { background: #ff9800; }
.status-paid { background: #4caf50; }
.status-shipped { background: #2196f3; }
.status-completed { background: #8bc34a; }
.status-cancelled { background: #f44336; }
.status-refunded { background: #9c27b0; }

/* 商品项样式 */
.goods-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
  background: #fafafa;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.goods-category {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-brief {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.goods-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 28rpx;
  color: #ff4757;
  font-weight: bold;
}

.goods-quantity {
  font-size: 26rpx;
  color: #666;
}

/* 金额行样式 */
.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.amount-row.total-row {
  border-bottom: none;
  border-top: 2px solid #2979ff;
  padding-top: 20rpx;
  margin-top: 10rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.amount-value.total {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
}

.remark-content {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
</style>
