import { defineStore } from 'pinia'
import { ref } from 'vue'

// 商品分类 Pinia Store
export const useGoodsCategoryStore = defineStore('goodsCategory', () => {
  const categories = ref([])
  const loading = ref(false)

  // 获取分类列表
  async function fetchCategories() {
    loading.value = true
    try {
      const { result } = await uniCloud.callFunction({
        name: 'categories',
        data: { action: 'get' }
      })
      // 按 sort 字段升序排序，若字段不存在则默认 0
      categories.value = (result.data || []).sort((a, b) => (a.sort || 0) - (b.sort || 0))
    } catch (e) {
      uni.showToast({ title: '加载分类失败', icon: 'none' })
    } finally {
      loading.value = false
    }
    console.log(categories.value,`获取了${categories.value.length}条数据`)
  }

  // 新增分类
  async function addCategory(data) {
    try {
      loading.value = true
      await uniCloud.callFunction({
        name: 'categories',
        data: {
          action: 'add',
          data
        }
      })
      uni.showToast({ title: '新增分类成功', icon: 'success' })
      // 重新拉取最新分类
      await fetchCategories()
    } catch (e) {
      uni.showToast({ title: '新增分类失败', icon: 'none' })
      console.error('新增分类失败:', e)
    } finally {
      loading.value = false
    }
  }

  // 更新分类
  async function updateCategory(id, data) {
    try {
      loading.value = true
      await uniCloud.callFunction({
        name: 'categories',
        data: {
          action: 'update',
          id,
          data
        }
      })
      uni.showToast({ title: '分类更新成功', icon: 'success' })
      await fetchCategories()
    } catch (e) {
      uni.showToast({ title: '分类更新失败', icon: 'none' })
      console.error('分类更新失败:', e)
    } finally {
      loading.value = false
    }
  }

  // 删除分类
  async function deleteCategory(id) {
    try {
      loading.value = true
      await uniCloud.callFunction({
        name: 'categories',
        data: {
          action: 'delete',
          id
        }
      })
      uni.showToast({ title: '分类删除成功', icon: 'success' })
      await fetchCategories()
    } catch (e) {
      uni.showToast({ title: '分类删除失败', icon: 'none' })
      console.error('分类删除失败:', e)
    } finally {
      loading.value = false
    }
  }

  return { categories, loading, fetchCategories, addCategory, updateCategory, deleteCategory }
})
