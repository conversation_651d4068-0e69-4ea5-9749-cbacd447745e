const crypto = require('crypto')

// 微信支付配置
const wxpayConfig = {
  appId: 'wx324a822fc64c5bd6',
  mchId: '1723079872',
  key: '8YRxAwnk8ZBVExDE8v5RQxdez6Xm6AxB'
}

// 生成随机字符串
function generateNonceStr() {
  return Math.random().toString(36).substr(2, 15)
}

// 生成签名
function generateSign(params) {
  // 排序参数
  const sortedKeys = Object.keys(params).sort()
  const stringA = sortedKeys.map(key => `${key}=${params[key]}`).join('&')
  const stringSignTemp = stringA + '&key=' + wxpayConfig.key

  // MD5加密并转大写
  return crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase()
}

// 生成小程序支付签名
function generatePaySign(params) {
  const { appId, timeStamp, nonceStr, package: packageStr, signType } = params
  const stringA = `appId=${appId}&nonceStr=${nonceStr}&package=${packageStr}&signType=${signType}&timeStamp=${timeStamp}`
  const stringSignTemp = stringA + '&key=' + wxpayConfig.key

  return crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase()
}

// 构建XML请求体
function buildXML(params) {
  let xml = '<xml>'
  for (const key in params) {
    xml += `<${key}><![CDATA[${params[key]}]]></${key}>`
  }
  xml += '</xml>'
  return xml
}

// 简单的XML解析
function parseXML(xml) {
  const result = {}
  const regex = /<(\w+)><!\[CDATA\[(.*?)\]\]><\/\1>|<(\w+)>(.*?)<\/\3>/g
  let match

  while ((match = regex.exec(xml)) !== null) {
    const key = match[1] || match[3]
    const value = match[2] || match[4]
    result[key] = value
  }

  return result
}

// 调用微信支付API
async function callWxpayAPI(xmlData) {
  try {
    const result = await uniCloud.httpclient.request('https://api.mch.weixin.qq.com/pay/unifiedorder', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/xml'
      },
      data: xmlData,
      dataType: 'text'
    })

    console.log('微信支付API响应:', result.data)

    // 解析XML响应
    return parseXML(result.data)

  } catch (error) {
    console.error('调用微信支付API失败:', error)
    throw error
  }
}

// 创建微信支付订单
async function createWxpayOrder(params) {
  try {
    const { total_fee, order_no, description, openid } = params

    // 生成随机字符串
    const nonce_str = generateNonceStr()

    // 当前时间戳
    const timeStamp = Math.floor(Date.now() / 1000).toString()

    // 构建支付参数
    const payParams = {
      appid: wxpayConfig.appId,
      mch_id: wxpayConfig.mchId,
      nonce_str: nonce_str,
      body: description,
      out_trade_no: order_no,
      total_fee: total_fee,
      spbill_create_ip: '127.0.0.1',
      notify_url: 'https://fc-mp-68e4a6b6-2e87-4e8c-a4e1-3f9b8c5d7f2a.next.bspapp.com/uni-pay-co',
      trade_type: 'JSAPI',
      openid: openid
    }

    console.log('使用 JSAPI 支付模式（小程序支付），openid:', openid)

    // 生成签名
    const sign = generateSign(payParams)
    payParams.sign = sign

    // 构建XML请求体
    const xmlData = buildXML(payParams)

    console.log('=== 微信支付请求参数 ===')
    console.log('完整参数:', payParams)
    console.log('支付金额 total_fee:', payParams.total_fee, '（分）')
    console.log('支付金额（元）:', (payParams.total_fee / 100).toFixed(2))
    console.log('订单号:', payParams.out_trade_no)
    console.log('商品描述:', payParams.body)

    // 调用微信支付统一下单接口
    const result = await callWxpayAPI(xmlData)

    if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
      // 构建小程序支付参数
      const paySign = generatePaySign({
        appId: wxpayConfig.appId,
        timeStamp: timeStamp,
        nonceStr: nonce_str,
        package: `prepay_id=${result.prepay_id}`,
        signType: 'MD5'
      })

      return {
        errCode: 0,
        errMsg: '支付订单创建成功',
        orderInfo: {
          timeStamp: timeStamp,
          nonceStr: nonce_str,
          package: `prepay_id=${result.prepay_id}`,
          signType: 'MD5',
          paySign: paySign
        },
        order_no: order_no
      }
    } else {
      return {
        errCode: -1,
        errMsg: result.err_code_des || result.return_msg || '微信支付下单失败'
      }
    }

  } catch (error) {
    console.error('创建微信支付订单失败:', error)
    return {
      errCode: -1,
      errMsg: '微信支付服务异常: ' + error.message
    }
  }
}

module.exports = {
  _before: function () {
    // 这里可以进行一些前置操作
  },

  /**
   * 创建支付订单
   * @param {Object} params 支付参数
   * @returns {Object} 支付结果
   */
  async createOrder(params) {
    try {
      console.log('=== uni-pay-co 创建支付订单开始 ===')
      console.log('接收到的支付参数:', params)
      console.log('支付金额 total_fee:', params.total_fee, '（应该是分为单位）')
      console.log('支付金额（元）:', (params.total_fee / 100).toFixed(2))

      const {
        provider,      // 支付供应商：wxpay、alipay
        total_fee,     // 支付金额（分）
        order_no,      // 订单号
        description,   // 商品描述
        type,          // 支付类型：goods、recharge等
        openid,        // 用户openid（微信支付需要）
        client_ip      // 客户端IP（可选）
      } = params

      // 参数验证
      const missingParams = []
      if (!provider) missingParams.push('provider')
      if (!total_fee) missingParams.push('total_fee')
      if (!order_no) missingParams.push('order_no')
      if (!description) missingParams.push('description')

      if (missingParams.length > 0) {
        return {
          errCode: -1,
          errMsg: `缺少必要参数: ${missingParams.join(', ')}`
        }
      }

      if (provider === 'wxpay') {
        // 微信支付
        return await createWxpayOrder({
          total_fee,
          order_no,
          description,
          openid: openid || 'test_openid'
        })
      } else {
        return {
          errCode: -1,
          errMsg: '暂不支持该支付方式'
        }
      }

    } catch (error) {
      console.error('uni-pay-co 创建支付订单失败:', error)
      return {
        errCode: -1,
        errMsg: '支付服务异常: ' + error.message
      }
    }
  },



  /**
   * 支付回调处理
   */
  async notifyCallback(params) {
    try {
      console.log('uni-pay-co 支付回调，参数:', params)

      // 这里应该验证回调签名和处理支付结果
      // 暂时返回成功
      return {
        errCode: 0,
        errMsg: 'success'
      }

    } catch (error) {
      console.error('uni-pay-co 支付回调处理失败:', error)
      return {
        errCode: -1,
        errMsg: '回调处理异常: ' + error.message
      }
    }
  }
}
