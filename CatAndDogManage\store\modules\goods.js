import { defineStore } from 'pinia'
import { ref } from 'vue'

// 商品管理 Pinia Store
export const useProductStore = defineStore('product', () => {
  const goodsList = ref([])
  const total = ref(0)
  const loading = ref(false)

  // 获取商品列表（自动分页，直到全部获取完）
  async function fetchList(params = {}) {
    loading.value = true
    try {
      const pageSize = params.pageSize || 20
      let page = 1
      let finished = false
      const all = []
      let totalRemote = 0
      while (!finished) {
        const { result } = await uniCloud.callFunction({
          name: 'goods',
          data: {
            action: 'get',
            page,
            pageSize,
            ...params
          }
        })
        const pageData = result.data || []
        totalRemote = result.total || pageData.length
        all.push(...pageData)
        if (all.length >= totalRemote || pageData.length < pageSize) {
          finished = true
        } else {
          page += 1
        }
      }
      goodsList.value = all
      total.value = totalRemote
      console.log(`已获取全部商品，共 ${total.value} 条`)
    } catch (e) {
      uni.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      loading.value = false
    }
  }

  // 新增商品
  async function addGoods(payload) {
    try {
      const { result } = await uniCloud.callFunction({
        name: 'goods',
        data: {
          action: 'add',
          ...payload
        }
      })
      if (result && result.code === 0) {
        // 云函数返回新记录 id
        goodsList.value.unshift({
          ...payload,
          _id: result.id,
          sales: payload.sales || 0,
          create_date: Date.now(),
          update_date: Date.now()
        })
        total.value += 1
      }
      return result
    } catch (e) {
      uni.showToast({ title: '新增失败', icon: 'none' })
      throw e
    }
  }

  // 删除商品
  async function deleteGoods(id) {
    if (!id) return { code: -1, msg: '缺少商品ID' }
    try {
      const { result } = await uniCloud.callFunction({
        name: 'goods',
        data: { action: 'delete', id }
      })
      if (result && result.code === 0) {
        const idx = goodsList.value.findIndex(item => item._id === id)
        if (idx !== -1) {
          goodsList.value.splice(idx, 1)
          total.value -= 1
        }
      }
      return result
    } catch (e) {
      uni.showToast({ title: '删除失败', icon: 'none' })
      throw e
    }
  }

  // 更新商品
  async function updateGoods(id, updateData) {
    if (!id) return { code: -1, msg: '缺少商品ID' }
    try {
      const { result } = await uniCloud.callFunction({
        name: 'goods',
        data: { action: 'update', id, updateData }
      })
      if (result && result.code === 0) {
        const item = goodsList.value.find(g => g._id === id)
        if (item) Object.assign(item, updateData)
      }
      return result
    } catch (e) {
      uni.showToast({ title: '更新失败', icon: 'none' })
      throw e
    }
  }

  return { goodsList, total, loading, fetchList, addGoods, deleteGoods, updateGoods }
}) 