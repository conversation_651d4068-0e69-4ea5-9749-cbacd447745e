<template>
  <view class="test-page">
    <view class="page-header">
      <text class="page-title">美观订单详情弹窗</text>
      <text class="page-desc">全新设计的订单详情编辑界面</text>
    </view>
    
    <view class="demo-section">
      <button type="primary" @click="showOrderDetail" class="demo-btn">
        查看订单详情
      </button>
    </view>
    
    <!-- 订单详情弹窗 -->
    <uni-popup ref="popup" type="center" :mask-click="true" @maskClick="handleMaskClick" :animation="true" :duration="300">
      <view class="order-detail-modal">
        <view v-if="selectedOrder" class="modal-container">
          <!-- 弹窗头部 -->
          <view class="modal-header">
            <view class="header-left">
              <text class="modal-title">订单详情</text>
              <text class="order-status" :class="'status-' + editOrder.status">{{ getStatusLabel(editOrder.status) }}</text>
            </view>
            <view class="header-right" @click="closeDetailPopup">
              <uni-icons type="close" size="20" color="#fff" />
            </view>
          </view>
          
          <!-- 弹窗内容 -->
          <scroll-view scroll-y class="modal-content">
            <!-- 订单基础信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="list" size="16" color="#2979ff" />
                <text>基础信息</text>
              </view>
              <view class="info-grid">
                <view class="info-item">
                  <text class="info-label">订单号</text>
                  <text class="info-value">{{ editOrder.orderNo || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">用户ID</text>
                  <input v-model="editOrder.userId" class="info-input" placeholder="请输入用户ID" />
                </view>
                <view class="info-item full-width">
                  <text class="info-label">订单标题</text>
                  <input v-model="editOrder.subject" class="info-input" placeholder="请输入订单标题" />
                </view>
                <view class="info-item">
                  <text class="info-label">下单时间</text>
                  <text class="info-value">{{ formatDate(editOrder.createTime) }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">订单状态</text>
                  <picker :range="statusOptions" range-key="label" :value="getStatusIndex(editOrder.status)" @change="onStatusPickerChange">
                    <view class="status-picker">
                      <text class="picker-text" :class="'status-' + editOrder.status">{{ getStatusLabel(editOrder.status) }}</text>
                      <uni-icons type="arrowdown" size="12" color="#999" />
                    </view>
                  </picker>
                </view>
              </view>
            </view>

            <!-- 收货信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="location" size="16" color="#4caf50" />
                <text>收货信息</text>
              </view>
              <view class="address-info">
                <view class="address-row">
                  <view class="address-item">
                    <text class="address-label">收货人</text>
                    <input v-model="editOrder.address.name" class="address-input" placeholder="收货人姓名" />
                  </view>
                  <view class="address-item">
                    <text class="address-label">联系电话</text>
                    <input v-model="editOrder.address.phone" class="address-input" placeholder="联系电话" />
                  </view>
                </view>
                <view class="address-row">
                  <view class="address-item full">
                    <text class="address-label">收货地区</text>
                    <input v-model="editOrder.address.region" class="address-input" placeholder="省市区" />
                  </view>
                </view>
                <view class="address-row">
                  <view class="address-item full">
                    <text class="address-label">详细地址</text>
                    <textarea v-model="editOrder.address.detail" class="address-textarea" placeholder="详细地址" />
                  </view>
                </view>
              </view>
            </view>

            <!-- 商品信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="shop" size="16" color="#ff9800" />
                <text>商品信息</text>
                <text class="goods-count">({{ editOrder.goods?.length || 0 }}件商品)</text>
              </view>
              <view class="goods-list">
                <view v-for="(item, index) in editOrder.goods" :key="index" class="goods-card">
                  <view class="goods-main">
                    <image :src="item.cover_image" class="goods-img" mode="aspectFill" />
                    <view class="goods-detail">
                      <view class="goods-title">{{ item.name || '商品名称' }}</view>
                      <view class="goods-meta">
                        <text class="goods-category">{{ item.category || '未分类' }}</text>
                        <text class="goods-brief">{{ item.brief || '暂无描述' }}</text>
                      </view>
                      <view class="goods-bottom">
                        <text class="goods-price">{{ formatMoney(item.price || 0) }}</text>
                        <view class="qty-controls">
                          <button class="qty-btn minus" @click="decreaseQuantity(index)">-</button>
                          <input v-model.number="item.num" class="qty-input" type="number" min="1" />
                          <button class="qty-btn plus" @click="increaseQuantity(index)">+</button>
                        </view>
                      </view>
                    </view>
                  </view>
                  <view class="goods-actions">
                    <button class="delete-btn" @click="removeGoods(index)">
                      <uni-icons type="trash" size="14" color="#ff4757" />
                    </button>
                  </view>
                </view>
              </view>
            </view>

            <!-- 金额信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="wallet" size="16" color="#e91e63" />
                <text>金额信息</text>
              </view>
              <view class="amount-list">
                <view class="amount-item">
                  <text class="amount-label">商品总额</text>
                  <text class="amount-value">{{ formatMoney(calculateGoodsTotal()) }}</text>
                </view>
                <view class="amount-item editable">
                  <text class="amount-label">优惠金额</text>
                  <view class="amount-input-wrapper">
                    <text class="currency">¥</text>
                    <input v-model="discountYuan" class="amount-input-field" type="digit" placeholder="0.00" @input="onDiscountInput" />
                  </view>
                </view>
                <view class="amount-item editable">
                  <text class="amount-label">运费</text>
                  <view class="amount-input-wrapper">
                    <text class="currency">¥</text>
                    <input v-model="shippingYuan" class="amount-input-field" type="digit" placeholder="0.00" @input="onShippingInput" />
                  </view>
                </view>
                <view class="amount-item total">
                  <text class="amount-label">实付金额</text>
                  <text class="amount-value highlight">{{ formatMoney(calculateActualAmount()) }}</text>
                </view>
              </view>
            </view>

            <!-- 备注信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="chat" size="16" color="#9c27b0" />
                <text>备注信息</text>
              </view>
              <textarea v-model="editOrder.remark" class="remark-textarea" placeholder="请输入订单备注信息..." />
            </view>
          </scroll-view>
          
          <!-- 弹窗底部 -->
          <view class="modal-footer">
            <button class="footer-btn cancel-btn" @click="closeDetailPopup">取消</button>
            <button class="footer-btn save-btn" @click="saveOrderChanges" :loading="saving">
              {{ saving ? '保存中...' : '保存修改' }}
            </button>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { formatMoney, centsToYuan, yuanToCents } from '@/utils/money.js'

export default {
  data() {
    return {
      selectedOrder: null,
      editOrder: {},
      saving: false,
      discountYuan: '0.00',
      shippingYuan: '0.00',
      statusOptions: [
        { value: 'pending', label: '待支付' },
        { value: 'paid', label: '已支付' },
        { value: 'shipped', label: '已发货' },
        { value: 'completed', label: '已完成' },
        { value: 'cancelled', label: '已取消' },
        { value: 'refunded', label: '已退款' }
      ],
      // 测试订单数据
      testOrderData: {
        "_id": "test_order_id",
        "orderNo": "ORDER_1753520592882_j8wlod",
        "userId": "6875070f2eea65b0f16e9897",
        "totalFee": 60800,
        "goods": [
          {
            "_id": "68723b3c6e5d2d42e6a9d95f",
            "name": "冠能幼犬粮",
            "category": "狗狗主粮",
            "price": 25900,
            "brief": "含初乳保护肠胃",
            "cover_image": "https://mp-4a6e8f25-9d84-4913-93ea-8d2d63f1e453.cdn.bspapp.com/goods-image/12.jpg",
            "num": 1
          },
          {
            "_id": "68723b37189f86d5e14e5ca9",
            "name": "伯纳天纯无谷",
            "category": "狗狗主粮",
            "price": 34900,
            "brief": "鲜肉+蔬果配方",
            "cover_image": "https://mp-4a6e8f25-9d84-4913-93ea-8d2d63f1e453.cdn.bspapp.com/goods-image/10.jpg",
            "num": 1
          }
        ],
        "address": {
          "name": "张三",
          "phone": "15894946891",
          "region": "山东省 东营市 利津县",
          "detail": "测试详细地址信息"
        },
        "status": "pending",
        "createTime": "2025-07-26T09:03:12.882Z",
        "subject": "购买2件商品",
        "discountAmount": 1000,
        "shippingFee": 500,
        "remark": "这是一个测试订单的备注信息，用于展示备注功能。"
      }
    }
  },
  
  methods: {
    // 显示订单详情
    showOrderDetail() {
      this.selectedOrder = this.testOrderData
      this.editOrder = JSON.parse(JSON.stringify(this.testOrderData))
      
      // 确保必要的字段存在
      if (!this.editOrder.address) {
        this.editOrder.address = { name: '', phone: '', region: '', detail: '' }
      }
      if (!this.editOrder.goods) {
        this.editOrder.goods = []
      }
      
      // 初始化金额显示（转换为元）
      this.discountYuan = centsToYuan(this.editOrder.discountAmount || 0)
      this.shippingYuan = centsToYuan(this.editOrder.shippingFee || 0)
      
      this.$refs.popup.open()
    },
    
    // 关闭详情弹窗
    closeDetailPopup() {
      this.$refs.popup.close()
      this.selectedOrder = null
      this.editOrder = {}
    },

    // 处理遮罩点击
    handleMaskClick() {
      this.closeDetailPopup()
    },
    
    // 获取状态标签
    getStatusLabel(status) {
      const found = this.statusOptions.find(opt => opt.value === status)
      return found ? found.label : status
    },
    
    // 获取状态索引
    getStatusIndex(status) {
      return this.statusOptions.findIndex(item => item.value === status)
    },
    
    // 状态选择器变化
    onStatusPickerChange(e) {
      const index = e.detail.value
      this.editOrder.status = this.statusOptions[index].value
    },
    
    // 增加商品数量
    increaseQuantity(index) {
      this.editOrder.goods[index].num += 1
    },
    
    // 减少商品数量
    decreaseQuantity(index) {
      if (this.editOrder.goods[index].num > 1) {
        this.editOrder.goods[index].num -= 1
      }
    },
    
    // 移除商品
    removeGoods(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该商品吗？',
        success: (res) => {
          if (res.confirm) {
            this.editOrder.goods.splice(index, 1)
          }
        }
      })
    },
    
    // 计算商品总额
    calculateGoodsTotal() {
      let total = 0
      this.editOrder.goods.forEach(item => {
        total += (item.price || 0) * (item.num || 0)
      })
      return total
    },
    
    // 计算实际支付金额
    calculateActualAmount() {
      const goodsTotal = this.calculateGoodsTotal()
      const discount = this.editOrder.discountAmount || 0
      const shipping = this.editOrder.shippingFee || 0
      return Math.max(0, goodsTotal - discount + shipping)
    },
    
    // 优惠金额输入处理
    onDiscountInput(e) {
      const yuan = parseFloat(e.detail.value) || 0
      this.editOrder.discountAmount = yuanToCents(yuan)
    },
    
    // 运费输入处理
    onShippingInput(e) {
      const yuan = parseFloat(e.detail.value) || 0
      this.editOrder.shippingFee = yuanToCents(yuan)
    },
    
    // 保存订单修改
    async saveOrderChanges() {
      this.saving = true
      try {
        // 模拟保存过程
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        console.log('保存的订单数据:', this.editOrder)
        this.closeDetailPopup()
      } catch (error) {
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    },
    
    // 格式化金额显示
    formatMoney(cents) {
      return formatMoney(cents)
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 60rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 80rpx;
  color: #fff;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.page-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

.demo-section {
  text-align: center;
}

.demo-btn {
  width: 400rpx;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 订单详情弹窗样式 */
.order-detail-modal {
  background: #fff;
  border-radius: 20rpx;
  width: 92vw;
  max-width: 750rpx;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.15);
}

.modal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-right {
  padding: 12rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.modal-content {
  flex: 1;
  padding: 0 30rpx;
  max-height: 65vh;
  background: #f8f9fa;
}

.info-card {
  background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.goods-count {
  margin-left: auto;
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-input {
  padding: 16rpx 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.info-input:focus {
  border-color: #667eea;
  outline: none;
}

.status-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
}

.picker-text {
  font-size: 28rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  color: #fff;
}

.picker-text.status-pending { background: #ff9800; }
.picker-text.status-paid { background: #4caf50; }
.picker-text.status-shipped { background: #2196f3; }
.picker-text.status-completed { background: #8bc34a; }
.picker-text.status-cancelled { background: #f44336; }
.picker-text.status-refunded { background: #9c27b0; }

.address-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.address-row {
  display: flex;
  gap: 20rpx;
}

.address-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.address-item.full {
  flex: 1;
}

.address-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.address-input {
  padding: 16rpx 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.address-textarea {
  padding: 16rpx 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  min-height: 80rpx;
  resize: none;
}

.goods-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.goods-card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1px solid #e9ecef;
}

.goods-main {
  display: flex;
  flex: 1;
  gap: 20rpx;
}

.goods-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
  background: #f0f0f0;
}

.goods-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.goods-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
}

.goods-meta {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.goods-category {
  font-size: 24rpx;
  color: #999;
}

.goods-brief {
  font-size: 26rpx;
  color: #666;
  line-height: 1.3;
}

.goods-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}

.goods-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #e91e63;
}

.qty-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.qty-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border: none;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qty-btn.minus {
  border-right: 1px solid #ddd;
}

.qty-btn.plus {
  border-left: 1px solid #ddd;
}

.qty-btn:active {
  background: #e0e0e0;
}

.qty-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: none;
  font-size: 28rpx;
  background: #fff;
}

.goods-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.delete-btn {
  width: 60rpx;
  height: 60rpx;
  background: #fff;
  border: 1px solid #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.amount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.amount-item.total {
  border-bottom: none;
  border-top: 2px solid #667eea;
  padding-top: 24rpx;
  margin-top: 12rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.amount-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.amount-value.highlight {
  font-size: 36rpx;
  color: #e91e63;
  font-weight: bold;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
  overflow: hidden;
}

.currency {
  padding: 16rpx 12rpx;
  background: #f8f9fa;
  color: #666;
  font-size: 28rpx;
  border-right: 1px solid #e0e0e0;
}

.amount-input-field {
  padding: 16rpx 20rpx;
  border: none;
  font-size: 28rpx;
  color: #333;
  width: 120rpx;
  text-align: right;
}

.remark-textarea {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  min-height: 120rpx;
  resize: none;
  line-height: 1.5;
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx 40rpx;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.footer-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.footer-btn:active {
  opacity: 0.8;
}
</style>
