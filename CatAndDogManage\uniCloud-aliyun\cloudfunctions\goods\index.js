'use strict';

exports.main = async (event, context) => {
  const db = uniCloud.database();
  const goodsCollection = db.collection('goods');

  switch (event.action) {
    case 'add':
      return await addGoods(event, goodsCollection);
    case 'delete':
      return await deleteGoods(event, goodsCollection);
    case 'update':
      return await updateGoods(event, goodsCollection);
    case 'get':
      return await getGoods(event, goodsCollection);
    default:
      return { code: -99, msg: '未知操作' };
  }
};

// 新增商品
async function addGoods(event, goodsCollection) {
  const { name, category, price, stock, status, description, cover_image } = event;
  if (!name || !category || price == null || stock == null || status == null) {
    return { code: -1, msg: '缺少必填字段' };
  }
  const res = await goodsCollection.add({
    name,
    category,
    price,
    stock,
    status,
    description: description || '',
    cover_image: cover_image || '',
    sales: 0,
    create_date: Date.now(),
    update_date: Date.now()
  });
  return { code: 0, msg: '添加成功', id: res.id };
}

// 删除商品
async function deleteGoods(event, goodsCollection) {
  const { id } = event;
  if (!id) return { code: -1, msg: '缺少商品ID' };
  await goodsCollection.doc(id).remove();
  return { code: 0, msg: '删除成功' };
}

// 修改商品
async function updateGoods(event, goodsCollection) {
  const { id, updateData } = event;
  if (!id || !updateData) return { code: -1, msg: '缺少参数' };
  updateData.update_date = Date.now();
  await goodsCollection.doc(id).update(updateData);
  return { code: 0, msg: '更新成功' };
}

// 获取商品
async function getGoods(event, goodsCollection) {
  const { id, filter, page = 1, pageSize = 10 } = event;
  if (id) {
    // 获取单个商品
    const res = await goodsCollection.doc(id).get();
    if (!res.data || res.data.length === 0) return { code: -2, msg: '商品不存在' };
    return { code: 0, data: res.data[0] };
  } else {
    // 获取商品列表，支持简单筛选和分页
    let query = goodsCollection;
    if (filter && typeof filter === 'object') {
      query = query.where(filter);
    }
    const total = await query.count();
    const res = await query.skip((page - 1) * pageSize).limit(pageSize).get();
    return { code: 0, data: res.data, total: total.total };
  }
}
