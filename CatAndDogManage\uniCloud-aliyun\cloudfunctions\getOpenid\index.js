'use strict';

/**
 * 获取用户 openid
 * @param {Object} event 
 * @returns {Object} 返回 openid
 */
exports.main = async (event, context) => {
  try {
    const { code } = event;
    
    if (!code) {
      return {
        code: -1,
        msg: '缺少登录凭证code'
      };
    }

    // 微信小程序配置
    const appId = 'wx324a822fc64c5bd6';
    const appSecret = 'c8bfd2457581d8794aa86d5d87c133cf';
    


    // 调用微信接口获取 openid
    const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;
    
    const result = await uniCloud.httpclient.request(url, {
      method: 'GET',
      dataType: 'json'
    });

    console.log('微信登录接口响应:', result.data);

    if (result.data.errcode) {
      return {
        code: -1,
        msg: '获取openid失败: ' + result.data.errmsg,
        errcode: result.data.errcode
      };
    }

    return {
      code: 0,
      msg: '获取成功',
      openid: result.data.openid,
      session_key: result.data.session_key,
      unionid: result.data.unionid || null
    };

  } catch (error) {
    console.error('获取openid异常:', error);
    return {
      code: -1,
      msg: '服务异常: ' + error.message
    };
  }
};
