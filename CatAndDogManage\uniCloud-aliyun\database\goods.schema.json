{"bsonType": "object", "required": ["name", "category", "price", "stock", "status", "brief", "cover_image", "sales", "create_date"], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "name": {"bsonType": "string", "title": "商品名称", "description": "商品名称", "maxLength": 100, "trim": "both"}, "category": {"bsonType": "string", "title": "分类", "description": "商品分类", "maxLength": 50, "trim": "both"}, "price": {"bsonType": "int", "title": "价格(分)", "description": "价格（以分为单位存储）", "minimum": 0}, "stock": {"bsonType": "int", "title": "库存", "description": "库存数量", "minimum": 0}, "status": {"bsonType": "int", "title": "状态", "description": "商品状态：1=上架 0=下架", "defaultValue": 1, "enum": [{"text": "上架", "value": 1}, {"text": "下架", "value": 0}]}, "brief": {"bsonType": "string", "title": "简要描述", "description": "商品简要描述", "maxLength": 255}, "description": {"bsonType": "string", "title": "详情", "description": "商品详情富文本，可存储HTML或Markdown", "format": "html"}, "cover_image": {"bsonType": "string", "title": "封面图", "description": "封面图片 URL", "format": "url", "trim": "both"}, "sales": {"bsonType": "int", "title": "销量", "description": "商品销量", "defaultValue": 0, "minimum": 0}, "create_date": {"bsonType": "timestamp", "title": "创建时间", "description": "创建时间戳", "forceDefaultValue": {"$env": "now"}}}}