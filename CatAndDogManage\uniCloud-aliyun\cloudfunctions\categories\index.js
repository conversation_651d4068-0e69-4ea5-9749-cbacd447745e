// 云函数：categories CRUD
'use strict';
const db = uniCloud.database()
const collection = db.collection('categories')

exports.main = async (event, context) => {
  const { action, data, id, filter, page = 1, pageSize = 20 } = event
  switch (action) {
    case 'add':
      // 新增分类
      return await collection.add(data)
    case 'delete':
      // 删除分类
      return await collection.doc(id).remove()
    case 'update':
      // 更新分类
      return await collection.doc(id).update(data)
    case 'get':
      // 查询分类，支持分页和条件
      const where = filter || {}
      const res = await collection.where(where).skip((page-1)*pageSize).limit(pageSize).get()
      return {
        code: 0,
        data: res.data,
        total: res.affectedDocs
      }
    case 'getById':
      // 根据ID获取单个分类
      return await collection.doc(id).get()
    default:
      return { code: 1, msg: '不支持的action' }
  }
} 