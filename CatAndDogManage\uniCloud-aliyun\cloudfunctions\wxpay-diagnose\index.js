'use strict';

const crypto = require('crypto');

/**
 * 微信支付配置诊断
 */
exports.main = async (event, context) => {
  try {
    console.log('开始微信支付配置诊断...');
    
    // 微信支付配置
    const wxpayConfig = {
      appId: 'wx324a822fc64c5bd6',
      mchId: '1723079872',
      key: '8YRxAwnk8ZBVExDE8v5RQxdez6Xm6AxB'
    };

    // 生成测试订单参数
    const testOrderNo = 'DIAGNOSE_' + Date.now();
    const nonce_str = Math.random().toString(36).substr(2, 15);
    
    // 构建最简单的支付参数进行测试
    const payParams = {
      appid: wxpayConfig.appId,
      mch_id: wxpayConfig.mchId,
      nonce_str: nonce_str,
      body: '诊断测试订单',
      out_trade_no: testOrderNo,
      total_fee: 1, // 1分钱
      spbill_create_ip: '127.0.0.1',
      notify_url: 'https://fc-mp-68e4a6b6-2e87-4e8c-a4e1-3f9b8c5d7f2a.next.bspapp.com/uni-pay-co',
      trade_type: 'JSAPI',
      openid: event.openid || 'test_openid_diagnose'
    };

    // 生成签名
    const sortedKeys = Object.keys(payParams).sort();
    const stringA = sortedKeys.map(key => `${key}=${payParams[key]}`).join('&');
    const stringSignTemp = stringA + '&key=' + wxpayConfig.key;
    const sign = crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase();
    payParams.sign = sign;

    // 构建XML
    let xml = '<xml>';
    for (const key in payParams) {
      xml += `<${key}><![CDATA[${payParams[key]}]]></${key}>`;
    }
    xml += '</xml>';

    console.log('诊断请求参数:', payParams);
    console.log('诊断请求XML:', xml);

    // 调用微信支付API
    const result = await uniCloud.httpclient.request('https://api.mch.weixin.qq.com/pay/unifiedorder', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/xml'
      },
      data: xml,
      dataType: 'text'
    });

    console.log('微信支付API原始响应:', result.data);

    // 解析XML响应
    const response = parseXML(result.data);
    console.log('解析后的响应:', response);

    return {
      code: 0,
      msg: '诊断完成',
      data: {
        request: payParams,
        response: response,
        analysis: analyzeResponse(response)
      }
    };

  } catch (error) {
    console.error('诊断异常:', error);
    return {
      code: -1,
      msg: '诊断异常: ' + error.message,
      error: error.toString()
    };
  }
};

// 解析XML
function parseXML(xml) {
  const result = {};
  const regex = /<(\w+)><!\[CDATA\[(.*?)\]\]><\/\1>|<(\w+)>(.*?)<\/\3>/g;
  let match;
  
  while ((match = regex.exec(xml)) !== null) {
    const key = match[1] || match[3];
    const value = match[2] || match[4];
    result[key] = value;
  }
  
  return result;
}

// 分析响应
function analyzeResponse(response) {
  const analysis = [];
  
  if (response.return_code !== 'SUCCESS') {
    analysis.push(`❌ 通信失败: ${response.return_msg}`);
  } else {
    analysis.push('✅ 通信成功');
  }
  
  if (response.result_code !== 'SUCCESS') {
    analysis.push(`❌ 业务失败: ${response.err_code} - ${response.err_code_des}`);
    
    // 具体错误分析
    switch (response.err_code) {
      case 'NOAUTH':
        analysis.push('🔍 权限问题：商户号未开通该产品权限或AppID配置错误');
        break;
      case 'APPID_NOT_EXIST':
        analysis.push('🔍 AppID问题：AppID不存在或配置错误');
        break;
      case 'MCHID_NOT_EXIST':
        analysis.push('🔍 商户号问题：商户号不存在或配置错误');
        break;
      case 'APPID_MCHID_NOT_MATCH':
        analysis.push('🔍 绑定问题：AppID与商户号未绑定');
        break;
      case 'SIGNERROR':
        analysis.push('🔍 签名问题：API密钥错误或签名算法错误');
        break;
      default:
        analysis.push(`🔍 其他错误：${response.err_code}`);
    }
  } else {
    analysis.push('✅ 业务成功');
  }
  
  return analysis;
}
