# 💰 金额单位处理说明

## 📋 概述

在订单管理系统中，为了避免浮点数精度问题和确保金额计算的准确性，我们采用以下金额单位处理策略：

- **数据库存储单位：分**
- **前端显示单位：元**
- **用户输入单位：元**

## 🔄 转换规则

### 基本转换
```javascript
// 元转分（存储到数据库）
299.00 元 → 29900 分

// 分转元（从数据库读取显示）
29900 分 → 299.00 元
```

### 精度处理
- 所有金额计算都在分的基础上进行
- 避免了 JavaScript 浮点数精度问题
- 确保金额计算的准确性

## 🛠️ 工具函数

### 核心函数
```javascript
import { 
  centsToYuan,      // 分转元
  yuanToCents,      // 元转分
  formatMoney,      // 格式化显示
  parseMoneyInput   // 解析用户输入
} from '@/utils/money.js'
```

### 使用示例

#### 1. 显示金额
```javascript
// 数据库返回的金额（分）
const totalFee = 29900

// 转换为元显示
const displayAmount = centsToYuan(totalFee) // "299.00"

// 格式化显示
const formattedAmount = formatMoney(totalFee) // "¥299.00"
```

#### 2. 处理用户输入
```javascript
// 用户输入的金额（元）
const userInput = "299.00"

// 转换为分存储
const storageAmount = yuanToCents(parseFloat(userInput)) // 29900

// 或使用解析函数
const storageAmount2 = parseMoneyInput(userInput) // 29900
```

#### 3. 金额计算
```javascript
// 计算实际支付金额
const totalFee = 29900      // 订单总额（分）
const discount = 1000       // 优惠金额（分）
const shipping = 500        // 运费（分）

const actualAmount = totalFee - discount + shipping // 29400（分）
```

## 📊 数据流向

### 创建订单流程
```
用户输入(元) → 转换为分 → 云函数处理 → 数据库存储(分)
```

### 显示订单流程
```
数据库查询(分) → Store处理 → 转换为元 → 前端显示(元)
```

## 🎯 应用场景

### 1. 订单创建
```javascript
// 前端页面
const orderData = {
  totalFee: yuanToCents(299.00),        // 29900分
  discountAmount: yuanToCents(10.00),   // 1000分
  shippingFee: yuanToCents(5.00)        // 500分
}

// 云函数自动计算实际金额
const actualAmount = totalFee - discountAmount + shippingFee // 29400分
```

### 2. 订单列表显示
```vue
<template>
  <view>
    <!-- 显示格式化金额 -->
    <text>{{ formatMoney(order.totalFee) }}</text>
  </view>
</template>

<script>
import { formatMoney } from '@/utils/money.js'

export default {
  methods: {
    formatMoney(cents) {
      return formatMoney(cents) // 自动转换并格式化
    }
  }
}
</script>
```

### 3. 统计计算
```javascript
// Store 中的统计计算
const orderStats = computed(() => {
  let totalAmount = 0 // 累加分值
  
  orderList.value.forEach(order => {
    if (order.status === 'paid' || order.status === 'completed') {
      totalAmount += order.actualAmount || order.totalFee || 0
    }
  })
  
  return {
    totalAmount, // 返回分值，显示时再转换
    // 其他统计...
  }
})
```

## ⚠️ 注意事项

### 1. 数据一致性
- 确保所有金额字段都使用分为单位
- 避免混用元和分导致的计算错误

### 2. 显示转换
- 所有前端显示都要进行分转元
- 使用统一的格式化函数

### 3. 输入验证
```javascript
// 验证用户输入
if (!isValidMoneyInput(userInput)) {
  uni.showToast({ title: '请输入有效金额', icon: 'error' })
  return
}
```

### 4. 精度控制
```javascript
// 避免直接使用浮点数计算
// ❌ 错误方式
const result = 299.00 - 10.00 + 5.00

// ✅ 正确方式
const totalCents = yuanToCents(299.00)
const discountCents = yuanToCents(10.00)
const shippingCents = yuanToCents(5.00)
const resultCents = totalCents - discountCents + shippingCents
```

## 🔧 工具函数详解

### formatMoney(cents, symbol)
```javascript
formatMoney(29900)        // "¥299.00"
formatMoney(29900, '$')   // "$299.00"
formatMoney(0)            // "¥0.00"
```

### formatMoneyWithComma(cents)
```javascript
formatMoneyWithComma(1234567)  // "¥12,345.67"
```

### MoneyCalculator 类
```javascript
const calculator = new MoneyCalculator()
calculator
  .add(yuanToCents(299.00))    // 添加299元
  .subtract(yuanToCents(10.00)) // 减去10元
  .add(yuanToCents(5.00))      // 添加5元

console.log(calculator.format()) // "¥294.00"
```

## 📝 最佳实践

1. **统一使用工具函数**：避免手动计算转换
2. **类型检查**：确保传入的是数字类型
3. **边界处理**：处理 null、undefined、NaN 等特殊值
4. **用户体验**：提供清晰的金额显示格式
5. **测试验证**：充分测试各种金额场景

## 🎉 总结

通过统一的金额单位处理策略，我们实现了：
- ✅ 精确的金额计算
- ✅ 一致的数据存储
- ✅ 友好的用户显示
- ✅ 可维护的代码结构

这套金额处理方案确保了订单管理系统中金额数据的准确性和一致性。
