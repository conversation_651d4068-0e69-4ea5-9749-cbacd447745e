<template>
  <view class="order-manage">
    <!-- 统计与图表区 -->
    <view class="top-flex-row">
      <view class="stat-cards">
        <view class="stat-card card-blue">
          <view class="stat-title">待发货</view>
          <view class="stat-value">{{ stat.waitShip }}</view>
        </view>
        <view class="stat-card card-orange">
          <view class="stat-title">待退货</view>
          <view class="stat-value">{{ stat.waitRefund }}</view>
        </view>
        <view class="stat-card card-green">
          <view class="stat-title">今日订单</view>
          <view class="stat-value">{{ stat.todayOrder }}</view>
        </view>
        <view class="stat-card card-red">
          <view class="stat-title">销售额</view>
          <view class="stat-value">￥{{ stat.todaySales }}</view>
        </view>
      </view>
      <view class="chart-card">
        <view class="chart-inner">
          <view class="chart-title">7天内订单数</view>
          <qiun-data-charts type="line" :opts="lineOpts" :chartData="lineChartData" />
        </view>
      </view>
    </view>
    <!-- 搜索 -->
    <view class="search-bar search-card">
      <view class="search-left">
        <input v-model="search.orderNo" placeholder="订单号" class="search-input" />
        <input v-model="search.customer" placeholder="客户名称" class="search-input" />
        <picker :range="statusOptions" range-key="label" v-model="search.status" @change="onStatusChange">
          <view class="picker">
            {{ statusOptions[search.status]?.label || '全部状态' }}
          </view>
        </picker>
        <button class="search-btn primary" @click="onSearch">搜索</button>
        <button class="search-btn" @click="onReset">重置</button>
      </view>
      <view class="search-right">
        <text class="total-count">共 {{ total }} 条数据</text>
      </view>
    </view>
    <!-- 表格 -->
    <view class="order-table">
      <view class="table-header">
        <text class="col order-no">订单号</text>
        <text class="col customer">客户</text>
        <text class="col amount">金额</text>
        <text class="col status">状态</text>
        <text class="col date">下单时间</text>
        <text class="col actions">操作</text>
      </view>
      <view v-if="pagedOrders.length === 0" class="empty-state">
        <view class="empty-icon">📋</view>
        <text class="empty-text">{{ hasSearchConditions ? '没有找到符合条件的订单' : '暂无订单数据' }}</text>
        <text v-if="hasSearchConditions" class="empty-tip">请尝试调整搜索条件</text>
      </view>
      <view v-for="order in pagedOrders" :key="order._id" class="table-row">
        <text class="col order-no">{{ order.orderNo }}</text>
        <text class="col customer">{{ order.address?.name || '-' }}</text>
        <text class="col amount">{{ formatMoneyYuan(order.actualAmount || order.totalAmount || order.totalFee || 0) }}</text>
        <text class="col status">{{ getStatusLabel(order.status) }}</text>
        <text class="col date">{{ formatDate(order.createTime) }}</text>
        <view class="col actions">
          <button class="edit-btn" size="mini" @click="viewDetail(order)">详情</button>
          <button class="delete-btn" size="mini" type="warn" @click="handleDeleteOrder(order._id)">删除</button>
        </view>
      </view>
    </view>
    <!-- 分页 -->
    <view class="pagination">
      <button :disabled="page === 1" @click="page--">上一页</button>
      <text>第 {{ page }} / {{ totalPages }} 页</text>
      <button :disabled="page === totalPages" @click="page++">下一页</button>
    </view>
    <!-- 订单详情弹窗 -->
    <uni-popup ref="popup" type="center" :mask-click="true" @maskClick="handleMaskClick" :animation="true" :duration="300">
      <view class="order-detail-modal">
        <view v-if="selectedOrder" class="modal-container">
          <!-- 弹窗头部 -->
          <view class="modal-header">
            <view class="header-left">
              <text class="modal-title">订单详情</text>
              <text class="order-status" :class="'status-' + editOrder.status">{{ getStatusLabel(editOrder.status) }}</text>
            </view>
            <view class="header-right" @click="closeDetailPopup">
              <uni-icons type="close" size="20" color="#999" />
            </view>
          </view>

          <!-- 弹窗内容 -->
          <scroll-view scroll-y class="modal-content">
            <!-- 订单基础信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="list" size="16" color="#2979ff" />
                <text>基础信息</text>
              </view>
              <view class="info-grid">
                <view class="info-item">
                  <text class="info-label">订单号</text>
                  <text class="info-value">{{ editOrder.orderNo || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">用户ID</text>
                  <input v-model="editOrder.userId" class="info-input" placeholder="请输入用户ID" />
                </view>
                <view class="info-item full-width">
                  <text class="info-label">订单标题</text>
                  <input v-model="editOrder.subject" class="info-input" placeholder="请输入订单标题" />
                </view>
                <view class="info-item">
                  <text class="info-label">下单时间</text>
                  <text class="info-value">{{ formatDate(editOrder.createTime) }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">订单状态</text>
                  <picker :range="statusOptions" range-key="label" :value="getStatusIndex(editOrder.status)" @change="onStatusPickerChange">
                    <view class="status-picker">
                      <text class="picker-text" :class="'status-' + editOrder.status">{{ getStatusLabel(editOrder.status) }}</text>
                      <uni-icons type="arrowdown" size="12" color="#999" />
                    </view>
                  </picker>
                </view>
              </view>
            </view>

            <!-- 收货信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="location" size="16" color="#4caf50" />
                <text>收货信息</text>
              </view>
              <view class="address-info">
                <view class="address-row">
                  <view class="address-item">
                    <text class="address-label">收货人</text>
                    <input v-model="editOrder.address.name" class="address-input" placeholder="收货人姓名" />
                  </view>
                  <view class="address-item">
                    <text class="address-label">联系电话</text>
                    <input v-model="editOrder.address.phone" class="address-input" placeholder="联系电话" />
                  </view>
                </view>
                <view class="address-row">
                  <view class="address-item full">
                    <text class="address-label">收货地区</text>
                    <input v-model="editOrder.address.region" class="address-input" placeholder="省市区" />
                  </view>
                </view>
                <view class="address-row">
                  <view class="address-item full">
                    <text class="address-label">详细地址</text>
                    <textarea v-model="editOrder.address.detail" class="address-textarea" placeholder="详细地址" />
                  </view>
                </view>
              </view>
            </view>

            <!-- 商品信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="shop" size="16" color="#ff9800" />
                <text>商品信息</text>
                <text class="goods-count">({{ editOrder.goods?.length || 0 }}件商品)</text>
              </view>
              <view class="goods-list">
                <view v-for="(item, index) in editOrder.goods" :key="index" class="goods-card">
                  <view class="goods-main">
                    <image :src="item.cover_image || '/static/default-goods.png'" class="goods-img" mode="aspectFill" />
                    <view class="goods-detail">
                      <view class="goods-title">{{ item.name || '商品名称' }}</view>
                      <view class="goods-meta">
                        <text class="goods-category">{{ item.category || '未分类' }}</text>
                        <text class="goods-brief">{{ item.brief || '暂无描述' }}</text>
                      </view>
                      <view class="goods-bottom">
                        <text class="goods-price">{{ formatMoney(item.price || 0) }}</text>
                        <view class="qty-controls">
                          <button class="qty-btn minus" @click="decreaseQuantity(index)">-</button>
                          <input v-model.number="item.num" class="qty-input" type="number" min="1" />
                          <button class="qty-btn plus" @click="increaseQuantity(index)">+</button>
                        </view>
                      </view>
                    </view>
                  </view>
                  <view class="goods-actions">
                    <button class="delete-btn" @click="removeGoods(index)">
                      <uni-icons type="trash" size="14" color="#ff4757" />
                    </button>
                  </view>
                </view>
              </view>
            </view>

            <!-- 金额信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="wallet" size="16" color="#e91e63" />
                <text>金额信息</text>
              </view>
              <view class="amount-list">
                <view class="amount-item">
                  <text class="amount-label">商品总额</text>
                  <text class="amount-value">{{ formatMoney(calculateGoodsTotal()) }}</text>
                </view>
                <view class="amount-item editable">
                  <text class="amount-label">优惠金额</text>
                  <view class="amount-input-wrapper">
                    <text class="currency">¥</text>
                    <input v-model="discountYuan" class="amount-input-field" type="digit" placeholder="0.00" @input="onDiscountInput" />
                  </view>
                </view>
                <view class="amount-item editable">
                  <text class="amount-label">运费</text>
                  <view class="amount-input-wrapper">
                    <text class="currency">¥</text>
                    <input v-model="shippingYuan" class="amount-input-field" type="digit" placeholder="0.00" @input="onShippingInput" />
                  </view>
                </view>
                <view class="amount-item total">
                  <text class="amount-label">实付金额</text>
                  <text class="amount-value highlight">{{ formatMoney(calculateActualAmount()) }}</text>
                </view>
              </view>
            </view>

            <!-- 备注信息卡片 -->
            <view class="info-card">
              <view class="card-title">
                <uni-icons type="chat" size="16" color="#9c27b0" />
                <text>备注信息</text>
              </view>
              <textarea v-model="editOrder.remark" class="remark-textarea" placeholder="请输入订单备注信息..." />
            </view>
          </scroll-view>

          <!-- 弹窗底部 -->
          <view class="modal-footer">
            <button class="footer-btn cancel-btn" @click="closeDetailPopup">取消</button>
            <button class="footer-btn save-btn" @click="saveOrderChanges" :loading="saving">
              {{ saving ? '保存中...' : '保存修改' }}
            </button>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import QiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
import { useOrderStore } from '@/store/modules/order.js'
import { storeToRefs } from 'pinia'
import { formatMoney, formatMoneyYuan, centsToYuan, yuanToCents } from '@/utils/money.js'

export default {
  components: {
    'qiun-data-charts': QiunDataCharts
  },
  setup() {
    const orderStore = useOrderStore()
    const { orderList, total, loading, orderStats } = storeToRefs(orderStore)
    const { fetchAllOrders, deleteOrder, updateOrderStatus } = orderStore

    return {
      orderStore,
      orderList,
      total,
      loading,
      orderStats,
      fetchAllOrders,
      deleteOrder: deleteOrder,
      updateOrderStatus
    }
  },
  data() {
    // 生成最近7天日期和模拟订单数
    const days = []
    const today = new Date()
    for (let i = 6; i >= 0; i--) {
      const d = new Date(today)
      d.setDate(today.getDate() - i)
      days.push(`${d.getMonth() + 1}/${d.getDate()}`)
    }
    const orderCounts = [5, 8, 6, 10, 7, 12, 9] // 可自定义模拟数据
    return {
      lineOpts: {
        legend: { position: 'bottom' },
        color: ['#2979ff'],
        extra: {
          line: {
            type: 'curve',
            width: 3,
            activeType: 'hollow',
            linearType: 'custom',
            animation: true
          }
        },
        yAxis: {
          min: 0
        }
      },
      lineChartData: {
        categories: days,
        series: [
          {
            name: '订单数',
            data: orderCounts
          }
        ]
      },
      search: {
        orderNo: '',
        customer: '',
        status: 0 // 默认选择第一个选项（全部状态）
      },
      statusOptions: [
        { value: '', label: '全部状态' },
        { value: 'pending', label: '待支付' },
        { value: 'paid', label: '已支付' },
        { value: 'shipped', label: '已发货' },
        { value: 'completed', label: '已完成' },
        { value: 'cancelled', label: '已取消' }
      ],
      page: 1,
      pageSize: 10,
      selectedOrder: null,
      editOrder: {}, // 编辑中的订单数据
      saving: false,
      discountYuan: '0.00', // 优惠金额（元）
      shippingYuan: '0.00'  // 运费（元）
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.pageSize) || 1
    },
    pagedOrders() {
      // 实现前端分页
      const start = (this.page - 1) * this.pageSize
      return this.orderList.slice(start, start + this.pageSize)
    },
    // 使用 store 中的统计数据
    stat() {
      return {
        waitShip: this.orderStats.paid || 0,
        waitRefund: this.orderStats.refunded || 0,
        todayOrder: this.orderStats.today || 0,
        // 将分转换为元显示
        todaySales: centsToYuan(this.orderStats.totalAmount || 0)
      }
    },
    // 判断是否有搜索条件
    hasSearchConditions() {
      return !!(this.search.orderNo.trim() ||
                this.search.customer.trim() ||
                (this.search.status && this.statusOptions[this.search.status]?.value))
    }
  },
  async mounted() {
    await this.loadAllOrders()
  },
  methods: {
    // 加载全部订单
    async loadAllOrders() {
      try {
        const params = {}

        // 添加搜索条件
        if (this.search.orderNo) {
          params.orderNo = this.search.orderNo.trim()
        }
        if (this.search.customer) {
          params.keyword = this.search.customer.trim() // 使用keyword参数进行客户名称搜索
        }
        if (this.search.status) {
          params.status = this.statusOptions[this.search.status]?.value
        }

        await this.fetchAllOrders(params)

        console.log('搜索参数:', params)
        console.log('搜索结果:', this.total, '条订单')
      } catch (error) {
        console.error('加载订单列表失败:', error)
      }
    },

    getStatusLabel(status) {
      const found = this.statusOptions.find(opt => opt.value === status)
      return found ? found.label : status
    },

    onStatusChange(e) {
      this.search.status = e.detail.value
    },

    async onSearch() {
      this.page = 1
      await this.loadAllOrders()
    },

    async onReset() {
      this.search = {
        orderNo: '',
        customer: '',
        status: 0 // 重置为第一个选项（全部状态）
      }
      this.page = 1
      await this.loadAllOrders()
    },

    viewDetail(order) {
      this.selectedOrder = order
      // 深拷贝订单数据用于编辑
      this.editOrder = JSON.parse(JSON.stringify(order))

      // 确保必要的字段存在
      if (!this.editOrder.address) {
        this.editOrder.address = {
          name: '',
          phone: '',
          region: '',
          detail: ''
        }
      }
      if (!this.editOrder.goods) {
        this.editOrder.goods = []
      }
      if (!this.editOrder.discountAmount) {
        this.editOrder.discountAmount = 0
      }
      if (!this.editOrder.shippingFee) {
        this.editOrder.shippingFee = 0
      }
      if (!this.editOrder.subject) {
        this.editOrder.subject = ''
      }
      if (!this.editOrder.remark) {
        this.editOrder.remark = ''
      }

      // 初始化金额显示（转换为元）
      this.discountYuan = this.centsToYuan(this.editOrder.discountAmount || 0)
      this.shippingYuan = this.centsToYuan(this.editOrder.shippingFee || 0)

      this.$refs.popup.open()
    },

    async handleDeleteOrder(orderId) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该订单吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await this.deleteOrder(orderId)
              // store 会自动重新加载数据
            } catch (error) {
              console.error('删除订单失败:', error)
            }
          }
        }
      })
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 格式化地址
    formatAddress(address) {
      if (!address) return '-'
      const { province, city, district, detail } = address
      return `${province || ''}${city || ''}${district || ''}${detail || ''}`
    },

    // 格式化金额显示（分转元）
    formatMoney(cents) {
      return formatMoney(cents)
    },

    // 格式化金额显示（已经是元）
    formatMoneyYuan(yuan) {
      return formatMoneyYuan(yuan)
    },

    // 分转元
    centsToYuan(cents) {
      return centsToYuan(cents)
    },

    // 元转分
    yuanToCents(yuan) {
      return yuanToCents(yuan)
    },

    // 优惠金额输入处理
    onDiscountInput(e) {
      const yuan = parseFloat(e.detail.value) || 0
      this.editOrder.discountAmount = this.yuanToCents(yuan)
    },

    // 运费输入处理
    onShippingInput(e) {
      const yuan = parseFloat(e.detail.value) || 0
      this.editOrder.shippingFee = this.yuanToCents(yuan)
    },

    // 关闭详情弹窗
    closeDetailPopup() {
      // 添加关闭动画
      const modalElement = document.querySelector('.order-detail-modal')
      if (modalElement) {
        modalElement.classList.add('closing')

        // 等待动画完成后关闭弹窗
        setTimeout(() => {
          this.$refs.popup.close()
          this.selectedOrder = null
          this.editOrder = {}
          // 移除动画类，为下次打开做准备
          modalElement.classList.remove('closing')
        }, 300)
      } else {
        // 如果找不到元素，直接关闭
        this.$refs.popup.close()
        this.selectedOrder = null
        this.editOrder = {}
      }
    },

    // 处理遮罩点击
    handleMaskClick() {
      this.closeDetailPopup()
    },

    // 获取状态索引
    getStatusIndex(status) {
      return this.statusOptions.findIndex(item => item.value === status)
    },

    // 状态选择器变化
    onStatusPickerChange(e) {
      const index = e.detail.value
      this.editOrder.status = this.statusOptions[index].value
    },

    // 增加商品数量
    increaseQuantity(index) {
      this.editOrder.goods[index].num += 1
    },

    // 减少商品数量
    decreaseQuantity(index) {
      if (this.editOrder.goods[index].num > 1) {
        this.editOrder.goods[index].num -= 1
      }
    },

    // 移除商品
    removeGoods(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该商品吗？',
        success: (res) => {
          if (res.confirm) {
            this.editOrder.goods.splice(index, 1)
          }
        }
      })
    },

    // 计算商品总额
    calculateGoodsTotal() {
      let total = 0
      this.editOrder.goods.forEach(item => {
        total += (item.price || 0) * (item.num || 0)
      })
      return total
    },

    // 计算实际支付金额
    calculateActualAmount() {
      const goodsTotal = this.calculateGoodsTotal()
      const discount = this.editOrder.discountAmount || 0
      const shipping = this.editOrder.shippingFee || 0
      return Math.max(0, goodsTotal - discount + shipping)
    },

    // 保存订单修改
    async saveOrderChanges() {
      this.saving = true
      try {
        // 更新总金额和实际金额
        this.editOrder.totalFee = this.calculateGoodsTotal()
        this.editOrder.actualAmount = this.calculateActualAmount()

        // 构建更新数据
        const updateData = {
          userId: this.editOrder.userId,
          subject: this.editOrder.subject,
          status: this.editOrder.status,
          address: this.editOrder.address,
          goods: this.editOrder.goods,
          totalFee: this.editOrder.totalFee,
          discountAmount: this.editOrder.discountAmount,
          shippingFee: this.editOrder.shippingFee,
          actualAmount: this.editOrder.actualAmount,
          remark: this.editOrder.remark
        }

        await this.orderStore.updateOrder(this.editOrder._id, updateData)

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        this.closeDetailPopup()
      } catch (error) {
        console.error('保存订单失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    }
  },

  watch: {
    // 页码变化时不需要重新加载数据，因为我们使用前端分页
    // page() {
    //   // 前端分页，不需要重新请求数据
    // }
  }
}
</script>

<style scoped>
.order-manage {
  padding: 20rpx;
}
.search-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-wrap: wrap;
  flex: 1;
}

.search-right {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.total-count {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}
.search-input {
  padding: 8rpx;
  border: 1px solid #eee;
  border-radius: 4rpx;
}
.picker {
  padding: 8rpx;
  border: 1px solid #eee;
  border-radius: 4rpx;
  min-width: 120rpx;
}
/* 表格样式优化 - 垂直水平居中 */
.order-table {
  border: 1px solid #e0e0e0;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.table-header, .table-row {
  display: flex;
  align-items: center;
  min-height: 88rpx;
}
.table-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-weight: bold;
  min-height: 100rpx;
}
.table-header .col, .table-row .col {
  border-right: 1px solid rgba(224, 224, 224, 0.3);
  border-bottom: 1px solid #e0e0e0;
  padding: 20rpx 16rpx;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 88rpx;
  
}
.table-header .col {
  border-bottom: none;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
}

.table-header .col:last-child, .table-row .col:last-child {
  border-right: none;
}
.table-row:last-child .col {
  border-bottom: none;
}
.col {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}
/* 列宽度和对齐方式 */
.order-no {
  flex: 2;
  justify-content: flex-start !important;
  text-align: left !important;
}

.customer {
  flex: 1.2;
}

.amount {
  flex: 1.2;
  justify-content: center !important;
  text-align: center !important;
  font-weight: bold;
  color: #e91e63;
}

.status {
  flex: 1;
}

.date {
  flex: 1.8;
}

.actions {
  flex: 1.5;
  min-width: 240rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
}
.table-row {
  transition: all 0.2s ease;
  min-height: 100rpx;
}

.table-row:hover {
  background: #f8f9fa;
  transform: translateY(-1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}
/* 订单详情弹窗样式 */
.order-detail-modal {
  background: #fff;
  border-radius: 24rpx;
  width: 95vw;
  max-width: 900rpx;
  max-height: 92vh;
  overflow: hidden;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10rpx);
  animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center center;
}

/* 弹窗进入动画 */
@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.7) translateY(-50rpx);
    filter: blur(10rpx);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05) translateY(-10rpx);
    filter: blur(2rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
}

/* 弹窗退出动画 */
@keyframes modalSlideOut {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.95) translateY(20rpx);
    filter: blur(2rpx);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateY(50rpx);
    filter: blur(8rpx);
  }
}

/* 弹窗关闭时的动画类 */
.order-detail-modal.closing {
  animation: modalSlideOut 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
}

/* 头部滑入动画 */
@keyframes headerSlideDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 内容淡入动画 */
@keyframes contentFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 底部滑入动画 */
@keyframes footerSlideUp {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}



.modal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 48rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  position: relative;
  overflow: hidden;
  animation: headerSlideDown 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s both;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.06)"/></svg>');
  pointer-events: none;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
  position: relative;
  z-index: 1;
}

.modal-title {
  font-size: 40rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.order-status {
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
  font-weight: 500;
}

.header-right {
  padding: 16rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.header-right:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
}

.modal-content {
  flex: 1;
  padding: 0 40rpx;
  max-height: 68vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #f0f2f5 100%);
  animation: contentFadeIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
}



/* 信息卡片样式 */
.info-card {
  background: #fff;
  border-radius: 20rpx;
  margin: 28rpx 0;
  padding: 40rpx;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.info-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  z-index: 1;
}

.card-title uni-icons {
  padding: 8rpx;
  border-radius: 12rpx;
  background: rgba(102, 126, 234, 0.1);
}

.goods-count {
  margin-left: auto;
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

/* 基础信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-input {
  padding: 20rpx 24rpx;
  border: 2px solid #e8ecf0;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #333;
  background: #fff;
  transition: all 0.2s ease;
}

.info-input:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  transform: translateY(-1rpx);
}

.status-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
}

.picker-text {
  font-size: 28rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  color: #fff;
}

.picker-text.status-pending { background: #ff9800; }
.picker-text.status-paid { background: #4caf50; }
.picker-text.status-shipped { background: #2196f3; }
.picker-text.status-completed { background: #8bc34a; }
.picker-text.status-cancelled { background: #f44336; }
.picker-text.status-refunded { background: #9c27b0; }

/* 商品项样式 */
.goods-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
  background: #fafafa;
  position: relative;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.goods-category {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-brief {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.goods-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 28rpx;
  color: #ff4757;
  font-weight: bold;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 6rpx;
  overflow: hidden;
}

.qty-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border: none;
  font-size: 32rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qty-btn:active {
  background: #e0e0e0;
}

.qty-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: none;
  font-size: 28rpx;
  background: #fff;
}

.remove-goods-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 60rpx;
  height: 60rpx;
  background: #fff;
  border: 1px solid #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 金额行样式 */
.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.amount-row.total-row {
  border-bottom: none;
  border-top: 2px solid #2979ff;
  padding-top: 20rpx;
  margin-top: 10rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.amount-value.total {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
}

.amount-input {
  width: 200rpx;
  padding: 12rpx;
  border: 1px solid #e0e0e0;
  border-radius: 6rpx;
  text-align: right;
  font-size: 28rpx;
}

.detail-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx 40rpx;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-save {
  background: #2979ff;
  color: #fff;
}

/* 地址信息样式 */
.address-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.address-row {
  display: flex;
  gap: 20rpx;
}

.address-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.address-item.full {
  flex: 1;
}

.address-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.address-input {
  padding: 16rpx 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.address-textarea {
  padding: 16rpx 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  min-height: 80rpx;
  resize: none;
}

/* 商品列表样式 */
.goods-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.goods-card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16rpx;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.goods-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.goods-card:hover {
  border-color: #667eea;
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.goods-card:hover::before {
  opacity: 1;
}

.goods-main {
  display: flex;
  flex: 1;
  gap: 20rpx;
}

.goods-img {
  width: 140rpx;
  height: 140rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
  background: #f0f0f0;
  border: 2px solid #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.goods-img:hover {
  transform: scale(1.05);
}

.goods-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.goods-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
}

.goods-meta {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.goods-category {
  font-size: 24rpx;
  color: #999;
}

.goods-brief {
  font-size: 26rpx;
  color: #666;
  line-height: 1.3;
}

.goods-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}

.goods-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #e91e63;
}

.qty-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.qty-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border: none;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qty-btn.minus {
  border-right: 1px solid #ddd;
}

.qty-btn.plus {
  border-left: 1px solid #ddd;
}

.qty-btn:active {
  background: #e0e0e0;
}

.qty-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: none;
  font-size: 28rpx;
  background: #fff;
}

.goods-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.delete-btn {
  width: 60rpx;
  height: 60rpx;
  background: #fff;
  border: 1px solid #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 金额信息样式 */
.amount-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.amount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.amount-item.total {
  border-bottom: none;
  border-top: 2px solid #667eea;
  padding-top: 24rpx;
  margin-top: 12rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.amount-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.amount-value.highlight {
  font-size: 40rpx;
  color: #e91e63;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(233, 30, 99, 0.2);
  background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
  overflow: hidden;
}

.currency {
  padding: 16rpx 12rpx;
  background: #f8f9fa;
  color: #666;
  font-size: 28rpx;
  border-right: 1px solid #e0e0e0;
}

.amount-input-field {
  padding: 16rpx 20rpx;
  border: none;
  font-size: 28rpx;
  color: #333;
  width: 120rpx;
  text-align: right;
}

/* 备注样式 */
.remark-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2px solid #e8ecf0;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #333;
  background: #fff;
  min-height: 140rpx;
  resize: none;
  line-height: 1.6;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.remark-textarea:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  transform: translateY(-1rpx);
}

/* 底部按钮样式 */
.modal-footer {
  display: flex;
  gap: 32rpx;
  padding: 40rpx 48rpx;
  background: linear-gradient(180deg, #fff 0%, #f8f9fa 100%);
  border-top: 1px solid #e8ecf0;
  backdrop-filter: blur(10rpx);
  animation: footerSlideUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s both;
}

.footer-btn {
  flex: 1;
  height: 96rpx;
  border-radius: 16rpx;
  font-size: 34rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.footer-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.footer-btn:hover::before {
  left: 100%;
}

.cancel-btn {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #666;
  border: 2px solid #e9ecef;
}

.cancel-btn:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
}

.save-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 35rpx rgba(102, 126, 234, 0.4);
}

.footer-btn:active {
  transform: translateY(0);
}
.capsule-btn {
  border-radius: 20rpx;
  padding: 0 28rpx;
  height: 48rpx;
  line-height: 48rpx;
  font-size: 26rpx;
  border: none;
  margin: 0 8rpx;
  background: #f5f5f5;
  color: #333;
  transition: background 0.2s, color 0.2s;
}
.capsule-btn.detail {
  background: #e6f7ff;
  color: #1890ff;
}
.capsule-btn.detail:active {
  background: #bae7ff;
}
.capsule-btn.delete {
  background: #fff1f0;
  color: #f5222d;
}
.capsule-btn.delete:active {
  background: #ffa39e;
}
/* 表格操作按钮样式 */
.edit-btn {
  background: #2979ff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 100rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  white-space: nowrap;
  writing-mode: horizontal-tb;
  text-orientation: mixed;
}
.edit-btn:hover {
  background: #1565c0;
  transform: translateY(-1rpx);
}
.delete-btn {
  background: #ff4757;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 100rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  writing-mode: horizontal-tb;
  text-orientation: mixed;
}
.delete-btn:hover {
  background: #ff3742;
  transform: translateY(-1rpx);
}
.search-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 24rpx 24rpx 16rpx 24rpx;
  margin-bottom: 24rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16rpx;
}
.search-input {
  margin-right: 0;
  padding: 10rpx 18rpx;
  border: 1px solid #e0e0e0;
  border-radius: 6rpx;
  font-size: 28rpx;
  background: #fafbfc;
  transition: border 0.2s;
  min-width: 180rpx;
  outline: none;
}
.search-input:focus {
  border: 1.5px solid #2979ff;
  background: #fff;
}
.picker {
  margin-right: 0;
  padding: 10rpx 18rpx;
  border: 1px solid #e0e0e0;
  border-radius: 6rpx;
  min-width: 160rpx;
  background: #fafbfc;
  font-size: 28rpx;
  color: #333;
}
.search-btn {
  border: none;
  border-radius: 6rpx;
  padding: 0 32rpx;
  height: 48rpx;
  line-height: 48rpx;
  font-size: 28rpx;
  background: #f5f5f5;
  color: #333;
  margin-left: 0;
  margin-right: 8rpx;
  transition: background 0.2s, color 0.2s;
  cursor: pointer;
}
.search-btn.primary {
  background: #2979ff;
  color: #fff;
}
.search-btn.primary:active {
  background: #1565c0;
}
.search-btn:active {
  background: #e6e6e6;
}
.top-flex-row {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
  align-items: stretch;
}
.stat-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 16rpx;
  flex: 4;
}
.stat-card {
  border-radius: 18px;
  box-shadow: 0 4px 18px rgba(0,0,0,0.08);
  padding: 32rpx 0 24rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 0;
  min-height: 110rpx;
  transition: box-shadow 0.2s, transform 0.2s;
  position: relative;
  overflow: hidden;
}
.stat-card:hover {
  box-shadow: 0 8px 28px rgba(0,0,0,0.13);
  transform: translateY(-4px) scale(1.03);
  z-index: 2;
}
.stat-title {
  font-size: 22rpx;
  color: #f5f5f5cc;
  margin-bottom: 10rpx;
  letter-spacing: 1px;
  font-weight: 500;
}
.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2px 8px rgba(0,0,0,0.10);
}
.card-blue {
  background: linear-gradient(135deg, #4f8aff 0%, #2979ff 100%);
}
.card-orange {
  background: linear-gradient(135deg, #ffb86c 0%, #ff7e5f 100%);
}
.card-green {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.card-red {
  background: linear-gradient(135deg, #ff6a6a 0%, #ff4e50 100%);
}
.chart-card {
  background: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%);
  border-radius: 18px;
  box-shadow: 0 4px 18px rgba(41,121,255,0.08);
  flex: 6;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 0 32rpx 0;
  transition: box-shadow 0.2s, transform 0.2s;
  position: relative;
  overflow: hidden;
}
.chart-card:hover {
  box-shadow: 0 8px 28px rgba(41,121,255,0.13);
  transform: translateY(-4px) scale(1.02);
  z-index: 2;
}
.chart-inner {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.chart-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2979ff;
  margin-bottom: 18rpx;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px rgba(41,121,255,0.08);
}
@media (max-width: 900px) {
  .top-flex-row {
    flex-direction: column;
    gap: 16rpx;
  }
  .stat-cards, .chart-card {
    width: 100%;
    min-width: 0;
  }
  .chart-card {
    margin-top: 8rpx;
  }
}
@media (max-width: 700px) {
  .search-card {
    flex-direction: column;
    align-items: stretch;
    gap: 12rpx;
  }
  .search-input, .picker, .search-btn {
    min-width: 0;
    width: 100%;
    margin-right: 0;
  }
}
</style> 