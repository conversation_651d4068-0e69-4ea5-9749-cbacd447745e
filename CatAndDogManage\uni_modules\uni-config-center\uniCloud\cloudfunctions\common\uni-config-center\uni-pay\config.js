module.exports = {
  // 支付回调地址配置
  "notifyUrl": {
    "default": "https://fc-mp-68e4a6b6-2e87-4e8c-a4e1-3f9b8c5d7f2a.next.bspapp.com/uni-pay-co"
  },
  
  // 微信支付配置
  "wxpay": {
    "mp": {
      "appId": "wx324a822fc64c5bd6",
      "mchId": "1723079872",
      "key": "8YRxAwnk8ZBVExDE8v5RQxdez6Xm6AxB",
      "pfx": "",
      "sandbox": false,
      "version": 3
    },
    "app": {
      "appId": "wx324a822fc64c5bd6",
      "mchId": "1723079872",
      "key": "8YRxAwnk8ZBVExDE8v5RQxdez6Xm6AxB",
      "pfx": "",
      "sandbox": false,
      "version": 3
    },
    "h5": {
      "appId": "wx324a822fc64c5bd6",
      "mchId": "1723079872",
      "key": "8YRxAwnk8ZBVExDE8v5RQxdez6Xm6AxB",
      "pfx": "",
      "sandbox": false,
      "version": 3
    }
  },
  
  // 支付宝配置（暂时留空）
  "alipay": {
    "mp": {
      "appId": "",
      "privateKey": "",
      "alipayPublicKey": "",
      "sandbox": false
    },
    "app": {
      "appId": "",
      "privateKey": "",
      "alipayPublicKey": "",
      "sandbox": false
    },
    "h5": {
      "appId": "",
      "privateKey": "",
      "alipayPublicKey": "",
      "sandbox": false
    }
  }
}
