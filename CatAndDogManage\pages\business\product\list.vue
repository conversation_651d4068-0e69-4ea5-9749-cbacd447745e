<template>
  <view class="product-list">
    <!-- 统计卡片区 -->
    <view class="stat-cards">
      <view class="stat-card">
        <view class="stat-title">商品总数</view>
        <view class="stat-value">{{ total }}</view>
      </view>
      <view class="stat-card">
        <view class="stat-title">上架商品</view>
        <view class="stat-value">{{ onSaleCount }}</view>
      </view>
      <view class="stat-card">
        <view class="stat-title">库存总量</view>
        <view class="stat-value">{{ totalStock }}</view>
      </view>
      <view class="stat-card">
        <view class="stat-title">库存预警</view>
        <view class="stat-value warning">{{ lowStockCount }}</view>
      </view>
    </view>

    <!-- 主体左右布局：左侧搜索+操作，右侧图表 -->
    <view class="main-flex-row">
      <!-- 左侧整体包裹 -->
      <view class="main-left">
        <view class="left-card">
          <view class="search-bar">
            <uni-forms ref="form" :model="searchForm" label-width="80px">
              <uni-forms-item label="商品名称">
                <uni-easyinput v-model="searchForm.name" placeholder="请输入商品名称" />
              </uni-forms-item>
              <uni-forms-item label="商品分类">
                <uni-data-select
                  v-model="searchForm.category"
                  :localdata="categoryOptions"
                  placeholder="请选择分类"
                />
              </uni-forms-item>
              <uni-forms-item label="商品状态">
                <uni-data-select
                  v-model="searchForm.status"
                  :localdata="statusOptions"
                  placeholder="请选择状态"
                />
              </uni-forms-item>
              <uni-forms-item label="时间范围">
                <uni-datetime-picker
                  v-model="searchForm.dateRange"
                  type="daterange"
                  placeholder="请选择时间范围"
                />
              </uni-forms-item>
              <view class="search-buttons">
                <button type="primary" @click="handleSearch">搜索</button>
                <button @click="handleReset">重置</button>
              </view>
            </uni-forms>
          </view>
          <view class="action-bar">
            <button type="primary" @click="handleAdd">新增商品</button>
            <button @click="openManageCategory">修改分类</button>
            <button @click="handleExport">导出数据</button>
            <button @click="handleRefresh">刷新</button>
          </view>
        </view>
      </view>
      <!-- 右侧：图表 -->
      <view class="main-right">
        <view class="chart-card">
          <qiun-data-charts type="pie" :opts="pieOpts" :chartData="categoryChartData" />
        </view>
      </view>
    </view>

    <!-- 数据表格卡片 -->
    <view class="table-card">
      <view class="custom-flex-table"> 
        <!-- 表头 -->
        <view class="flex-table-header flex-table-row">
          <view class="flex-table-cell flex-table-img">商品图片</view>
          <view class="flex-table-cell">商品名称</view>
          <view class="flex-table-cell">分类</view>
          <view class="flex-table-cell sortable-cell" @click="sortBy('price')">
            价格
            <span class="sort-icons">
              <span :class="['sort-arrow', sortKey==='price' && sortOrder==='asc' ? 'active' : '']">▲</span>
              <span :class="['sort-arrow', sortKey==='price' && sortOrder==='desc' ? 'active' : '']">▼</span>
            </span>
          </view>
          <view class="flex-table-cell sortable-cell" @click="sortBy('stock')">
            库存
            <span class="sort-icons">
              <span :class="['sort-arrow', sortKey==='stock' && sortOrder==='asc' ? 'active' : '']">▲</span>
              <span :class="['sort-arrow', sortKey==='stock' && sortOrder==='desc' ? 'active' : '']">▼</span>
            </span>
          </view>
          <view class="flex-table-cell sortable-cell" @click="sortBy('sales')">
            销量
            <span class="sort-icons">
              <span :class="['sort-arrow', sortKey==='sales' && sortOrder==='asc' ? 'active' : '']">▲</span>
              <span :class="['sort-arrow', sortKey==='sales' && sortOrder==='desc' ? 'active' : '']">▼</span>
            </span>
          </view>
          <view class="flex-table-cell">状态</view>
          <view class="flex-table-cell sortable-cell" @click="sortBy('create_date')">
            创建时间
            <span class="sort-icons">
              <span :class="['sort-arrow', sortKey==='create_date' && sortOrder==='asc' ? 'active' : '']">▲</span>
              <span :class="['sort-arrow', sortKey==='create_date' && sortOrder==='desc' ? 'active' : '']">▼</span>
            </span>
          </view>
          <view class="flex-table-cell">操作</view>
        </view>
        <!-- 空数据 -->
        <view v-if="goodsList.length === 0" class="flex-table-row flex-table-empty">
          <view class="flex-table-cell" style="text-align:center;" :colspan="9">暂无数据</view>
        </view>
        <!-- 数据 -->
        <view v-for="item in pagedTableData" :key="item._id" class="flex-table-row">
          <view class="flex-table-cell flex-table-img">
            <image :src="item.cover_image" class="product-image" mode="aspectFill" />
          </view>
          <view class="flex-table-cell">{{ item.name }}</view>
          <view class="flex-table-cell">{{ getCategoryText(item.category) }}</view>
          <view class="flex-table-cell">¥{{ (item.price / 100).toFixed(2) }}</view>
          <view class="flex-table-cell">{{ item.stock }}</view>
          <view class="flex-table-cell">{{ item.sales }}</view>
          <view class="flex-table-cell">
            <view :class="['status-tag', item.status === 1 ? 'on' : 'off']">
              {{ getStatusText(item.status) }}
            </view>
          </view>
          <view class="flex-table-cell">{{ formatDate(item.create_date) }}</view>
          <view class="flex-table-cell">
            <button class="edit-btn" size="mini" @click="handleEdit(item)">编辑</button>
            <button size="mini" type="warn" @click="handleDelete(item)">删除</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 分页 -->
    <uni-pagination
      :total="displayTotal"
      :pageSize="pageSize"
      :current="currentPage"
      @change="handlePageChange"
    />

    <!-- 新增/编辑弹窗 -->
    <uni-popup ref="formPopup" type="center" :mask-click="true" @maskClick="closeFormPopup">
      <view class="form-popup">
        <view class="popup-header">
          <text class="popup-title">{{ isEdit ? '编辑商品' : '新增商品' }}</text>
        </view>
        <uni-icons type="close" size="32" class="popup-close-x" @click="closeFormPopup" />
        <view class="popup-content-scroll">
          <uni-forms ref="formRef" :model="formData" :rules="formRules" label-width="100px">
            <uni-forms-item label="商品名称" required>
              <uni-easyinput v-model="formData.name" placeholder="请输入商品名称" />
            </uni-forms-item>
            <uni-forms-item label="商品图片">
              <image :src="formData.cover_image" class="product-image-upload" v-if="formData.cover_image" />
              <button size="mini" @click="handleChooseImage">选择图片</button>
            </uni-forms-item>
            <uni-forms-item label="分类">
              <uni-data-select
                v-model="formData.category"
                :localdata="categoryOptions"
                placeholder="请选择分类"
              />
            </uni-forms-item>
            <uni-forms-item label="售价(分)">
              <uni-number-box v-model="formData.price" :min="0" :max="999999999" :step="1" />
              <text class="price-hint">显示价格：¥{{ (formData.price / 100).toFixed(2) }}</text>
            </uni-forms-item>
            <uni-forms-item label="原价(分)">
              <uni-number-box v-model="formData.original_price" :min="0" :max="999999999" :step="1" />
              <text class="price-hint" v-if="formData.original_price > 0">显示价格：¥{{ (formData.original_price / 100).toFixed(2) }}</text>
            </uni-forms-item>
            <uni-forms-item label="库存">
              <uni-number-box v-model="formData.stock" :min="0" :step="1" />
            </uni-forms-item>
            <uni-forms-item label="销量">
              <uni-number-box v-model="formData.sales" :min="0" :step="1" />
            </uni-forms-item>
            <uni-forms-item label="状态" required>
              <uni-data-select
                v-model="formData.status"
                :localdata="statusOptions"
                placeholder="请选择状态"
              />
            </uni-forms-item>
            <uni-forms-item label="商品简介">
              <uni-easyinput
                v-model="formData.brief"
                type="textarea"
                placeholder="请输入商品简介"
                maxlength="100"
              />
            </uni-forms-item>
            <uni-forms-item label="商品详情">
              <uni-easyinput
                v-model="formData.description"
                type="textarea"
                placeholder="请输入商品详情"
              />
            </uni-forms-item>
          </uni-forms>
        </view>
        <view class="popup-footer">
          <button @click="closeFormPopup">取消</button>
          <button type="primary" @click="handleSubmit">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 分类管理弹窗 -->
    <uni-popup ref="categoryManagePopup" type="center" :mask-click="true" @maskClick="closeManageCategory">
      <view class="category-manage-popup">
        <view class="popup-header">
          <text class="popup-title">分类管理</text>
        </view>
        <uni-icons type="close" size="32" class="popup-close-x" @click="closeManageCategory" />
        <view class="popup-content-scroll">
          <view class="category-list">
            <view class="category-item" v-for="cat in categories" :key="cat._id" :class="{ 'selected': selectedCategoryIds.includes(cat._id) }">
              <view class="check-wrapper" @click.stop="toggleSelect(cat._id)">
                <view :class="['check-circle', selectedCategoryIds.includes(cat._id) ? 'checked' : '']"></view>
              </view>

              <uni-easyinput v-if="editingId === cat._id" v-model="editName" class="edit-input" placeholder="请输入分类名称" />
              <view v-else class="category-name" @click.stop="toggleSelect(cat._id)">{{ cat.name }}</view>

              <!-- 排序显示/编辑 -->
              <uni-number-box v-if="editingId === cat._id" v-model="editSort" :min="0" :step="1" class="sort-input" />
              <view v-else class="category-sort">{{ cat.sort ?? 0 }}</view>

              <button class="edit-btn mini" size="mini" @click.stop="editingId===cat._id ? saveEdit(cat) : startEdit(cat)">
                {{ editingId===cat._id ? '保存' : '编辑' }}
              </button>
            </view>
          </view>
        </view>
        <view class="popup-footer">
          <button @click="closeManageCategory">取消</button>
          <button type="primary" @click="openAddCategory">新增分类</button>
          <button type="warn" @click="deleteSelectedCategories">删除选中的分类</button>
        </view>
      </view>
    </uni-popup>

    <!-- 新增分类弹窗 -->
    <uni-popup ref="addCategoryPopup" type="center" :mask-click="true" @maskClick="closeAddCategory">
      <view class="form-popup small-form-popup">
        <view class="popup-header">
          <text class="popup-title">新增分类</text>
        </view>
        <uni-icons type="close" size="32" class="popup-close-x" @click="closeAddCategory" />
        <view class="popup-content-scroll">
          <uni-forms ref="addCategoryFormRef" :model="addCategoryFormData" :rules="addCategoryRules" label-width="80px">
            <uni-forms-item label="分类名称" required>
              <uni-easyinput v-model="addCategoryFormData.name" placeholder="请输入分类名称" />
            </uni-forms-item>
            <uni-forms-item label="排序号" required>
              <uni-number-box v-model="addCategoryFormData.sort" :min="0" :step="1" />
            </uni-forms-item>
          </uni-forms>
        </view>
        <view class="popup-footer">
          <button @click="closeAddCategory">取消</button>
          <button type="primary" @click="submitAddCategory">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useProductStore } from '@/store/modules/goods'
import { useGoodsCategoryStore } from '@/store/modules/goodsCategory'
import { storeToRefs } from 'pinia'
import QiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'

const productStore = useProductStore()
const { goodsList, total, loading } = storeToRefs(productStore)
// 新增：直接使用 store 的增删改方法
const { fetchList, addGoods, updateGoods: updateGoodsAction } = productStore

// 分类 store
const categoryStore = useGoodsCategoryStore()
const { categories, loading: categoryLoading } = storeToRefs(categoryStore)

onMounted(() => {
  fetchList()
  categoryStore.fetchCategories()
})

// 商品分类选项：使用分类名称作为 value，便于直接存入商品记录中
const categoryOptions = computed(() => {
  return categories.value.map(c => ({ value: c.name, text: c.name }))
})
// 商品状态选项
const statusOptions = [
  { value: 1, text: '上架' },
  { value: 0, text: '下架' }
]

// 搜索表单（避免 undefined 错误）
const searchForm = ref({
  name: '',
  category: '',
  status: '',
  dateRange: []
})

const pageSize = ref(10)
const currentPage = ref(1)

// 表单数据
const formData = ref({
  name: '',
  cover_image: '',
  category: '',
  price: 0,
  original_price: 0,
  brief: '',
  description: '',
  stock: 0,
  sales: 0,
  status: 0
})
const isEdit = ref(false)
const editId = ref(null)

// 表单验证规则
const formRules = {
  name: {
    rules: [
      { required: true, errorMessage: '请输入商品名称' }
    ]
  },
  status: {
    rules: [
      { required: true, errorMessage: '请选择状态' }
    ]
  },
  price: {
    rules: [
      { validateFunction: (rule, value, data, callback) => {
        if (value < 0) {
          callback('价格不能为负数')
        }
        return true
      }}
    ]
  },
  original_price: {
    rules: [
      { validateFunction: (rule, value, data, callback) => {
        if (value < 0) {
          callback('原价不能为负数')
        }
        if (value > 0 && value < data.price) {
          callback('原价不能低于售价')
        }
        return true
      }}
    ]
  }
}

// 图表配置
const pieOpts = {
  legend: { position: 'bottom' },
  extra: { pie: { labelWidth: 15 } }
}
const categoryChartData = ref({
  series: []
})

const sortKey = ref('')
const sortOrder = ref('') // 'asc' or 'desc'

// 计算属性（筛选）
const filteredData = computed(() => {
  let arr = goodsList.value
  // 名称模糊匹配
  if (searchForm.value.name.trim()) {
    const keyword = searchForm.value.name.trim().toLowerCase()
    arr = arr.filter(item => (item.name || '').toLowerCase().includes(keyword))
  }
  // 分类精确匹配
  if (searchForm.value.category) {
    const selName = searchForm.value.category
    // 找到对应分类对象，兼容旧 _id 存储
    const catObj = categories.value.find(c => c.name === selName)
    arr = arr.filter(item => {
      if (item.category === selName) return true
      if (catObj && item.category === catObj._id) return true
      return false
    })
  }
  // 状态匹配
  if (searchForm.value.status !== '') {
    arr = arr.filter(item => item.status === searchForm.value.status)
  }
  // 时间范围
  if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
    const [start, end] = searchForm.value.dateRange
    const startTime = new Date(start).getTime()
    const endTime = new Date(end).getTime()
    arr = arr.filter(item => {
      const t = new Date(item.create_date).getTime()
      return t >= startTime && t <= endTime
    })
  }
  return arr
})

// 计算属性（排序）
const sortedTableData = computed(() => {
  if (!sortKey.value) return filteredData.value

  const arr = [...filteredData.value]
  arr.sort((a, b) => {
    let v1 = a[sortKey.value]
    let v2 = b[sortKey.value]
    // 日期特殊处理
    if (sortKey.value === 'create_date') {
      v1 = new Date(v1).getTime()
      v2 = new Date(v2).getTime()
    }
    if (sortOrder.value === 'asc') return v1 > v2 ? 1 : -1
    if (sortOrder.value === 'desc') return v1 < v2 ? 1 : -1
    return 0
  })
  return arr
})
// 计算属性（分页）
const pagedTableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return sortedTableData.value.slice(start, start + pageSize.value)
})
// 分页总数（随筛选变化）
const displayTotal = computed(() => filteredData.value.length)
// 上架商品数
const onSaleCount = computed(() => goodsList.value.filter(item => item.status === 1).length)
// 库存总量
const totalStock = computed(() => goodsList.value.reduce((sum, item) => sum + (item.stock || 0), 0))
// 库存预警（库存<=10）
const lowStockCount = computed(() => goodsList.value.filter(item => item.stock <= 10).length)

// ref
const formPopup = ref()
const formRef = ref()

// ------------------ 分类管理 ------------------
// 弹窗、状态
const categoryManagePopup = ref()
const selectedCategoryIds = ref([])
const editingId = ref(null)
const editName = ref('')
const editSort = ref(0)

const { addCategory: addCategoryAction, updateCategory: updateCategoryAction, deleteCategory: deleteCategoryAction } = categoryStore

function openManageCategory() {
  // 初始化状态
  selectedCategoryIds.value = []
  editingId.value = null
  editName.value = ''
  editSort.value = 0
  categoryManagePopup.value.open()
}

function closeManageCategory() {
  categoryManagePopup.value.close()
}

function toggleSelect(id) {
  const idx = selectedCategoryIds.value.indexOf(id)
  if (idx === -1) {
    selectedCategoryIds.value.push(id)
  } else {
    selectedCategoryIds.value.splice(idx, 1)
  }
}

function startEdit(cat) {
  editingId.value = cat._id
  editName.value = cat.name
  editSort.value = cat.sort ?? 0
}

async function saveEdit(cat) {
  if (!editName.value.trim()) {
    uni.showToast({ title: '名称不能为空', icon: 'none' })
    return
  }
  await updateCategoryAction(cat._id, { name: editName.value, sort: editSort.value })
  editingId.value = null
  editName.value = ''
  editSort.value = 0
}

async function deleteSelectedCategories() {
  if (selectedCategoryIds.value.length === 0) {
    uni.showToast({ title: '请选择要删除的分类', icon: 'none' })
    return
  }
  uni.showModal({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedCategoryIds.value.length} 个分类吗？`,
    success: async res => {
      if (res.confirm) {
        for (const id of selectedCategoryIds.value) {
          await deleteCategoryAction(id)
        }
        selectedCategoryIds.value = []
      }
    }
  })
}

// 方法
function updateCategoryChart() {
  const categoryMap = {}
  goodsList.value.forEach(item => {
    categoryMap[item.category] = (categoryMap[item.category] || 0) + 1
  })
  const series = Object.keys(categoryMap).map(key => ({
    name: getCategoryText(key),
    value: categoryMap[key]
  }))
  categoryChartData.value = { series }
}

function handleSearch() {
  currentPage.value = 1
  // 前端 computed 已实时筛选，无需重新拉取
}
function handleReset() {
  searchForm.value = {
    name: '',
    category: '',
    status: '',
    dateRange: []
  }
  currentPage.value = 1
  // 前端 computed 将自动恢复
}
function handleAdd() {
  isEdit.value = false
  editId.value = null
  formData.value = {
    name: '',
    cover_image: '',
    category: '',
    price: 0,
    original_price: 0,
    brief: '',
    description: '',
    stock: 0,
    sales: 0,
    status: 0
  }
  formPopup.value.open()
}
function handleView(row) {
  uni.showModal({
    title: '商品详情',
    content: `商品名称：${row.name}\n分类：${getCategoryText(row.category)}\n价格：¥${(row.price / 100).toFixed(2)}\n原价：¥${(row.original_price / 100).toFixed(2)}\n库存：${row.stock}\n销量：${row.sales}\n状态：${getStatusText(row.status)}`,
    showCancel: false
  })
}
function handleEdit(row) {
  isEdit.value = true
  editId.value = row._id
  // 直接回显分类字段，无需类型转换
  formData.value = { ...row }
  formPopup.value.open()
}
function handleDelete(row) {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除商品 ${row.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        // TODO: 删除逻辑对接
      }
    }
  })
}
function handleExport() {
  uni.showToast({ title: '导出功能开发中', icon: 'none' })
}
function handleRefresh() {
  fetchList()
}
function handlePageChange(e) {
  currentPage.value = e.current
}
async function handleSubmit() {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 深拷贝，以免影响表单数据
    const payload = { ...formData.value }
    // 删除 _id 避免被误写入
    delete payload._id

    if (isEdit.value) {
      await updateGoodsAction(editId.value, payload)
    } else {
      await addGoods(payload)
    }

    closeFormPopup()
    uni.showToast({ title: isEdit.value ? '编辑成功' : '新增成功', icon: 'success' })
    updateCategoryChart()
  } catch (error) {
    console.error('表单提交失败:', error)
  }
}
function closeFormPopup() {
  formPopup.value.close()
}
function handleChooseImage() {
  uni.chooseImage({
    count: 1,
    success: async (res) => {
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        const tempPath = res.tempFilePaths[0]
        try {
          // 获取文件扩展名
          let ext = '.jpg'
          if (res.tempFiles && res.tempFiles[0]) {
            const name = res.tempFiles[0].name || ''
            const idx = name.lastIndexOf('.')
            if (idx !== -1) ext = name.substring(idx)
          } else {
            const idx = tempPath.lastIndexOf('.')
            if (idx !== -1 && tempPath.substring(idx).length <= 6) {
              ext = tempPath.substring(idx)
            }
          }
          // 生成 cloudPath，确保只包含字母数字和可接受符号
          const randStr = Math.random().toString(36).slice(2, 8)
          const cloudPath = `goods-image/${Date.now()}_${randStr}${ext}`
          const uploadOptions = { cloudPath }
          if (res.tempFiles && res.tempFiles[0] && res.tempFiles[0].file) {
            // H5 平台需要传 file 对象
            uploadOptions.file = res.tempFiles[0].file
          } else {
            uploadOptions.filePath = tempPath
          }
          const uploadRes = await uniCloud.uploadFile(uploadOptions)
          // fileID 即可直接作为访问 URL 使用
          formData.value.cover_image = uploadRes.fileID
        } catch (err) {
          uni.showToast({ title: '图片上传失败', icon: 'none' })
          console.log(err)
        }
      }
    }
  })
}
function getCategoryText(val) {
  // 首先按名称匹配
  let item = categoryOptions.value.find(item => item.value === val)
  if (item) return item.text
  // 再尝试按 _id 匹配，兼容旧数据
  const cat = categories.value.find(c => c._id === val)
  return cat ? cat.name : val
}
function getStatusText(val) {
  const item = statusOptions.find(item => item.value === val)
  return item ? item.text : val
}
function getStatusType(val) {
  const typeMap = {
    1: 'success',
    0: 'default'
  }
  return typeMap[val] || 'default'
}
function formatDate(date) {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleDateString() + ' ' + d.toLocaleTimeString()
}
function sortBy(key) {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortKey.value = key
    sortOrder.value = 'asc'
  }
}

// ---------- 新增分类弹窗 ----------
const addCategoryPopup = ref()
const addCategoryFormRef = ref()
const addCategoryFormData = ref({ name: '', sort: 0 })
const addCategoryRules = {
  name: { rules: [ { required: true, errorMessage: '请输入分类名称' } ] },
  sort: { rules: [ { required: true, errorMessage: '请输入排序号' } ] }
}

function openAddCategory() {
  addCategoryFormData.value = { name: '', sort: 0 }
  addCategoryPopup.value.open()
}

function closeAddCategory() {
  addCategoryPopup.value.close()
}

async function submitAddCategory() {
  try {
    const valid = await addCategoryFormRef.value.validate()
    if (!valid) return
    await addCategoryAction({ ...addCategoryFormData.value })
    closeAddCategory()
  } catch (e) {
    console.error(e)
  }
}
</script>

<style lang="scss" scoped>
.product-list {
  padding: 20px;
  background: #f7f8fa;
}

.stat-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}
.stat-card {
  flex: 1 1 180px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  padding: 24px 20px 18px 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 160px;
}
.stat-title {
  font-size: 14px;
  color: #888;
  margin-bottom: 8px;
}
.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}
.stat-value.warning {
  color: #faad14;
}

.chart-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  margin-bottom: 24px;
  padding: 20px 10px 10px 10px;
  min-height: 260px;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  button {
    flex: 1;
  }
}

.action-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  button {
    flex: 1;
  }
}

.table-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  padding: 0;
  margin-bottom: 20px;
  overflow-x: auto;
  overflow: hidden;
}

::v-deep .uni-table {
  font-size: 14px;
  background: #fff;
}

::v-deep .uni-table-thead {
  background: #f7f8fa;
  font-weight: bold;
}

::v-deep .uni-table-th {
  border-bottom: 1px solid #f0f0f0;
  color: #333;
  font-weight: 500;
  padding: 10px 8px;
}

::v-deep .uni-table-td {
  border-bottom: 1px solid #f0f0f0;
  color: #444;
  padding: 10px 8px;
}

::v-deep .uni-table-tr {
  transition: background 0.2s;
}
::v-deep .uni-table-tr:hover {
  background: #f5f7fa;
}

::v-deep .uni-table .uni-table-selection {
  padding-left: 8px;
  padding-right: 8px;
}

.product-image {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  object-fit: cover;
  background: #f5f5f5;
}

.product-image-upload {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  background: #f5f5f5;
  margin-bottom: 10px;
}

.price-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.price {
  color: #f56c6c;
  font-weight: bold;
  font-size: 14px;
}

.original-price {
  color: #999;
  font-size: 12px;
  text-decoration: line-through;
}

.price-hint {
  font-size: 12px;
  color: #666;
  margin-left: 10px;
}

.action-buttons {
  display: flex;
  gap: 5px;
  button {
    margin: 0;
  }
}

.form-popup {
  background: #fff;
  border-radius: 8px;
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  height: 80vh;
  position: relative;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  z-index: 10;
}
.popup-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}
.popup-content-scroll {
  flex: 1 1 auto;
  overflow-y: auto;
  min-height: 0;
  padding: 0 0 0 0;
}
.popup-footer {
  display: flex;
  gap: 16px;
  padding: 20px;
  border-top: 1px solid #eee;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.04);
  z-index: 10;
}
.popup-footer button {
  flex: 1;
  height: 44px;
  font-size: 16px;
  border-radius: 24px;
  border: none;
  outline: none;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(41,121,255,0.04);
  cursor: pointer;
}
.popup-footer button[type="primary"] {
  background: linear-gradient(90deg, #2979ff 0%, #4f8aff 100%);
  color: #fff;
  font-weight: bold;
}
.popup-footer button[type="primary"]:hover {
  background: linear-gradient(90deg, #1565c0 0%, #2979ff 100%);
}
.popup-footer button:not([type="primary"]) {
  background: #f5f5f5;
  color: #666;
}
.popup-footer button:not([type="primary"]):hover {
  background: #e0e0e0;
  color: #2979ff;
}
:deep(.uni-forms) {
  padding: 20px;
}

.main-flex-row {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  align-items: stretch;
}
.main-left, .main-right {
  flex: 1 1 0;
  min-width: 0;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.left-card, .chart-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
@media (max-width: 900px) {
  .main-flex-row {
    flex-direction: column;
  }
  .main-left, .main-right {
    max-width: 100%;
    min-width: 0;
  }
}
.custom-flex-table {
  width: 100%;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  background: #fff;
}
.flex-table-header, .flex-table-row {
  display: flex;
  /* 保持默认 stretch，避免边框断裂 */
  /* align-items:center; */
  /* 不设置background和border-radius */
}
.flex-table-header {
  font-weight: bold;
  color: #333;
}
.flex-table-header .flex-table-cell, .flex-table-row .flex-table-cell {
  border-right: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  padding: 10px 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.flex-table-header .flex-table-cell:last-child, .flex-table-row .flex-table-cell:last-child {
  border-right: none;
}
.flex-table-cell {
  flex: 1 1 0;
  display: flex;
  align-items: center; /* 垂直居中内容 */
  justify-content: center; /* 水平居中，如需左对齐改为 flex-start */
  text-align: center;
  word-break: break-all;
  /* 不设置background */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.flex-table-row .flex-table-cell:last-child {
  min-width: 180px;
}
.flex-table-row {
  min-height: 44px;
  transition: background 0.2s;
}
.flex-table-row:hover {
  background: #f5f7fa;
}
.status-tag {
  display: inline-block;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 12px;
  color: #fff;
}
.status-tag.on {
  background: #52c41a;
}
.status-tag.off {
  background: #aaa;
}
.flex-table-empty .flex-table-cell {
  color: #bbb;
  font-size: 15px;
  padding: 20px 0;
}
@media (max-width: 900px) {
  .flex-table-header, .flex-table-row {
    font-size: 13px;
    min-height: 36px;
  }
  .flex-table-row .flex-table-cell:last-child {
    min-width: 120px;
  }
}
.edit-btn {
  background: #2979ff;
  color: #fff;
  border: none;
  border-radius: 4px;
  
  margin-right: 8px;
  font-size: 13px;
  cursor: pointer;
  transition: background 0.2s;
}
.edit-btn:hover {
  background: #1565c0;
}
.flex-table-header .flex-table-cell:not(:last-child) {
  /* 移除特殊分割线，保持和内容区一致 */
  border-right: 1px solid #e0e0e0;
}
.flex-table-header .flex-table-cell {
  position: relative;
  background: #f7f8fa;
  font-weight: bold;
  color: #333;
  z-index: 1;
}
.flex-table-img {
  min-width: 60px;
  max-width: 80px;
  width: 70px;
  padding: 0 4px;
}
.sortable-cell {
  cursor: pointer;
  user-select: none;
  transition: background 0.2s;
}
.sortable-cell:hover {
  background: #e6f0ff;
}
.sort-icons {
  display: inline-block;
  margin-left: 4px;
  font-size: 12px;
  vertical-align: middle;
}
.sort-arrow {
  display: inline-block;
  color: #bbb;
  margin-left: 1px;
  font-size: 12px;
  transition: color 0.2s;
}
.sort-arrow.active {
  color: #2979ff;
}
// 调整各列宽度
.flex-table-header .flex-table-cell:nth-child(7), .flex-table-row .flex-table-cell:nth-child(7) { /* 状态栏 */
  flex: 0.7;
  min-width: 60px;
  max-width: 80px;
}
.flex-table-header .flex-table-cell:nth-child(9), .flex-table-row .flex-table-cell:nth-child(9) { /* 操作栏 */
  flex: 1.2;
  min-width: 120px;
  max-width: 160px;
}
.flex-table-header .flex-table-cell:nth-child(8), .flex-table-row .flex-table-cell:nth-child(8) { /* 创建时间栏 */
  flex: 1.6;
  min-width: 140px;
}
.popup-close-x {
  position: absolute;
  top: 18px;
  right: 18px;
  font-size: 32px !important;
  color: #888;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background 0.2s;
  z-index: 20;
}
.popup-close-x:hover {
  background: #f0f0f0;
  color: #2979ff;
}

/* 新增分类小弹窗尺寸 */
.small-form-popup {
  width: 280px;
  max-width: 90vw;
  height: auto;
}

.category-manage-popup {
  background: #fff;
  border-radius: 8px;
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  height: 80vh;
  position: relative;
}
.category-list {
  flex: none;
  max-height: 60vh; /* 占弹窗高度约 3/4 (弹窗 80vh) */
  overflow-y: auto;
  padding: 10px 0;
}
.category-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background 0.2s;
}
.category-item:last-child {
  border-bottom: none;
}
.category-item:hover {
  background: #f5f7fa;
}
.category-item.selected {
  background: #e6f0ff;
  font-weight: bold;
}
.check-wrapper {
  margin-right: 10px;
}
.check-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #bbb;
  box-sizing: border-box;
}
.check-circle.checked {
  background: #2979ff;
  border-color: #2979ff;
}
.check-circle.checked::after {
  content: '';
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #fff;
  margin: 2px auto;
}
.edit-input {
  width: 200px;
  flex: none;
  margin-right: 10px;
}
.category-name {
  width: 200px; /* 与输入框一致的宽度 */
  flex: none;
  margin-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.category-sort {
  width: 60px;
  text-align: center;
  margin-left: 10px;
}
.sort-input {
  width: 80px;
  margin-left: 10px;
}

/* 调整 uni-data-select 弹窗高度 */
:deep(.uni-select__selector-scroll) {
  max-height: 20vh; /* 显示高度占屏幕 3/4 ，多余可滚动 */
}
.delete-icon {
  color: #f56c6c;
  margin-left: 10px;
  cursor: pointer;
  transition: color 0.2s;
}
.delete-icon:hover {
  color: #f78989;
}
</style> 