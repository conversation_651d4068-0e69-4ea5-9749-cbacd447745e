<template>
  <view class="payment-detail">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-left">
        <uni-icons type="left" size="20" @click="goBack" />
        <text class="header-title">支付详情</text>
      </view>
      <view class="header-actions">
        <button size="mini" type="primary" @click="handleEdit">编辑</button>
        <button size="mini" type="warn" @click="handleDelete">删除</button>
      </view>
    </view>

    <!-- 支付信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">支付信息</text>
        <uni-tag :text="getPaymentStatusText(paymentInfo.paymentStatus)" :type="getPaymentStatusType(paymentInfo.paymentStatus)" />
      </view>
      
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">订单号</text>
          <text class="info-value">{{ paymentInfo.orderNo }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">支付金额</text>
          <text class="info-value amount">¥{{ paymentInfo.amount?.toFixed(2) || '0.00' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">支付方式</text>
          <text class="info-value">{{ getPaymentMethodText(paymentInfo.paymentMethod) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">支付状态</text>
          <text class="info-value">{{ getPaymentStatusText(paymentInfo.paymentStatus) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{ paymentInfo.createTime }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">支付时间</text>
          <text class="info-value">{{ paymentInfo.payTime || '未支付' }}</text>
        </view>
        <view class="info-item full-width">
          <text class="info-label">备注</text>
          <text class="info-value">{{ paymentInfo.remark || '无' }}</text>
        </view>
      </view>
    </view>

    <!-- 操作记录 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">操作记录</text>
      </view>
      
      <view class="operation-list">
        <view 
          v-for="(operation, index) in operationLogs" 
          :key="index"
          class="operation-item"
        >
          <view class="operation-icon">
            <uni-icons :type="getOperationIcon(operation.type)" size="16" />
          </view>
          <view class="operation-content">
            <text class="operation-text">{{ operation.text }}</text>
            <text class="operation-time">{{ operation.time }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 相关订单信息 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">相关订单</text>
      </view>
      
      <view class="order-info">
        <view class="order-item">
          <text class="order-label">订单编号</text>
          <text class="order-value">{{ orderInfo.orderNo }}</text>
        </view>
        <view class="order-item">
          <text class="order-label">商品信息</text>
          <text class="order-value">{{ orderInfo.productInfo }}</text>
        </view>
        <view class="order-item">
          <text class="order-label">订单状态</text>
          <text class="order-value">{{ orderInfo.orderStatus }}</text>
        </view>
        <view class="order-item">
          <text class="order-label">下单时间</text>
          <text class="order-value">{{ orderInfo.createTime }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PaymentDetail',
  data() {
    return {
      paymentId: null,
      paymentInfo: {
        orderNo: '',
        amount: 0,
        paymentMethod: '',
        paymentStatus: '',
        createTime: '',
        payTime: '',
        remark: ''
      },
      operationLogs: [],
      orderInfo: {
        orderNo: '',
        productInfo: '',
        orderStatus: '',
        createTime: ''
      },
      
      // 支付方式选项
      paymentMethods: [
        { value: 'alipay', text: '支付宝' },
        { value: 'wechat', text: '微信支付' },
        { value: 'bank', text: '银行转账' },
        { value: 'cash', text: '现金支付' },
        { value: 'other', text: '其他' }
      ],
      
      // 支付状态选项
      paymentStatuses: [
        { value: 'pending', text: '待支付' },
        { value: 'success', text: '支付成功' },
        { value: 'failed', text: '支付失败' },
        { value: 'cancelled', text: '已取消' },
        { value: 'refunded', text: '已退款' }
      ]
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.paymentId = options.id
      this.loadPaymentDetail()
    }
  },
  
  methods: {
    // 加载支付详情
    async loadPaymentDetail() {
      try {
        // 模拟API调用
        await this.mockLoadDetail()
      } catch (error) {
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    
    // 模拟加载详情数据
    mockLoadDetail() {
      return new Promise((resolve) => {
        setTimeout(() => {
          // 模拟支付信息
          this.paymentInfo = {
            orderNo: `ORDER${String(this.paymentId || 1).padStart(6, '0')}`,
            amount: Math.random() * 1000 + 100,
            paymentMethod: ['alipay', 'wechat', 'bank', 'cash', 'other'][Math.floor(Math.random() * 5)],
            paymentStatus: ['pending', 'success', 'failed', 'cancelled', 'refunded'][Math.floor(Math.random() * 5)],
            createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString(),
            payTime: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString() : '',
            remark: Math.random() > 0.5 ? '测试备注信息' : ''
          }
          
          // 模拟操作记录
          this.operationLogs = [
            {
              type: 'create',
              text: '创建支付记录',
              time: this.paymentInfo.createTime
            },
            {
              type: 'edit',
              text: '修改支付信息',
              time: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toLocaleString()
            }
          ]
          
          // 模拟订单信息
          this.orderInfo = {
            orderNo: this.paymentInfo.orderNo,
            productInfo: 'iPhone 15 Pro Max 256GB 深空黑色',
            orderStatus: '已完成',
            createTime: this.paymentInfo.createTime
          }
          
          resolve()
        }, 500)
      })
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 编辑
    handleEdit() {
      uni.navigateTo({
        url: `/pages/finance/payment/edit?id=${this.paymentId}`
      })
    },
    
    // 删除
    handleDelete() {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除订单 ${this.paymentInfo.orderNo} 的支付记录吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
            setTimeout(() => {
              this.goBack()
            }, 1500)
          }
        }
      })
    },
    
    // 获取支付方式文本
    getPaymentMethodText(method) {
      const item = this.paymentMethods.find(item => item.value === method)
      return item ? item.text : method
    },
    
    // 获取支付状态文本
    getPaymentStatusText(status) {
      const item = this.paymentStatuses.find(item => item.value === status)
      return item ? item.text : status
    },
    
    // 获取支付状态类型
    getPaymentStatusType(status) {
      const typeMap = {
        pending: 'warning',
        success: 'success',
        failed: 'error',
        cancelled: 'info',
        refunded: 'default'
      }
      return typeMap[status] || 'default'
    },
    
    // 获取操作图标
    getOperationIcon(type) {
      const iconMap = {
        create: 'plus',
        edit: 'compose',
        delete: 'trash',
        pay: 'wallet'
      }
      return iconMap[type] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-detail {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.info-card {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.info-grid {
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  
  &.full-width {
    grid-column: 1 / -1;
  }
}

.info-label {
  font-size: 12px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  
  &.amount {
    color: #f56c6c;
    font-weight: bold;
  }
}

.operation-list {
  padding: 20px;
}

.operation-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.operation-icon {
  width: 32px;
  height: 32px;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.operation-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.operation-text {
  font-size: 14px;
  color: #333;
}

.operation-time {
  font-size: 12px;
  color: #999;
}

.order-info {
  padding: 20px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.order-label {
  font-size: 14px;
  color: #666;
}

.order-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}
</style> 