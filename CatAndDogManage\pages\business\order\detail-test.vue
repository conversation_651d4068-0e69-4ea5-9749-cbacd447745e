<template>
  <view class="test-page">
    <view class="page-header">
      <text class="page-title">订单详情弹窗测试</text>
      <text class="page-desc">测试新的订单详情编辑功能</text>
    </view>
    
    <view class="test-section">
      <button type="primary" @click="showTestOrder">显示测试订单详情</button>
    </view>
    
    <!-- 订单详情弹窗 -->
    <uni-popup ref="popup" type="center" :mask-click="false">
      <view class="order-detail-popup">
        <view v-if="selectedOrder" class="detail-container">
          <!-- 弹窗头部 -->
          <view class="detail-header">
            <text class="detail-title">订单详情</text>
            <uni-icons type="close" size="24" color="#666" @click="closeDetailPopup" />
          </view>
          
          <!-- 弹窗内容 -->
          <scroll-view scroll-y class="detail-content">
            <!-- 基本信息 -->
            <view class="detail-section">
              <view class="section-title">基本信息</view>
              <view class="form-group">
                <text class="label">订单号</text>
                <input v-model="editOrder.orderNo" class="input readonly" readonly />
              </view>
              <view class="form-group">
                <text class="label">下单用户ID</text>
                <input v-model="editOrder.userId" class="input" placeholder="请输入用户ID" />
              </view>
              <view class="form-group">
                <text class="label">订单标题</text>
                <input v-model="editOrder.subject" class="input" placeholder="请输入订单标题" />
              </view>
              <view class="form-group">
                <text class="label">订单状态</text>
                <picker :range="statusOptions" range-key="label" :value="getStatusIndex(editOrder.status)" @change="onStatusPickerChange">
                  <view class="picker-input">
                    <text>{{ getStatusLabel(editOrder.status) }}</text>
                    <uni-icons type="arrowdown" size="14" color="#999" />
                  </view>
                </picker>
              </view>
              <view class="form-group">
                <text class="label">下单时间</text>
                <input :value="formatDate(editOrder.createTime)" class="input readonly" readonly />
              </view>
            </view>

            <!-- 收货信息 -->
            <view class="detail-section">
              <view class="section-title">收货信息</view>
              <view class="form-group">
                <text class="label">收货人</text>
                <input v-model="editOrder.address.name" class="input" placeholder="请输入收货人姓名" />
              </view>
              <view class="form-group">
                <text class="label">联系电话</text>
                <input v-model="editOrder.address.phone" class="input" placeholder="请输入联系电话" />
              </view>
              <view class="form-group">
                <text class="label">收货地区</text>
                <input v-model="editOrder.address.region" class="input" placeholder="请输入收货地区" />
              </view>
              <view class="form-group">
                <text class="label">详细地址</text>
                <textarea v-model="editOrder.address.detail" class="textarea" placeholder="请输入详细地址" />
              </view>
            </view>

            <!-- 商品信息 -->
            <view class="detail-section">
              <view class="section-title">商品信息</view>
              <view v-for="(item, index) in editOrder.goods" :key="index" class="goods-item">
                <image :src="item.cover_image" class="goods-image" mode="aspectFill" />
                <view class="goods-info">
                  <view class="goods-name">{{ item.name }}</view>
                  <view class="goods-category">{{ item.category }}</view>
                  <view class="goods-brief">{{ item.brief }}</view>
                  <view class="goods-price-row">
                    <text class="goods-price">{{ formatMoney(item.price) }}</text>
                    <view class="quantity-control">
                      <button class="qty-btn" @click="decreaseQuantity(index)">-</button>
                      <input v-model.number="item.num" class="qty-input" type="number" />
                      <button class="qty-btn" @click="increaseQuantity(index)">+</button>
                    </view>
                  </view>
                </view>
                <button class="remove-goods-btn" @click="removeGoods(index)">
                  <uni-icons type="trash" size="16" color="#ff4757" />
                </button>
              </view>
            </view>

            <!-- 金额信息 -->
            <view class="detail-section">
              <view class="section-title">金额信息</view>
              <view class="amount-row">
                <text class="amount-label">商品总额</text>
                <text class="amount-value">{{ formatMoney(calculateGoodsTotal()) }}</text>
              </view>
              <view class="amount-row">
                <text class="amount-label">优惠金额</text>
                <input v-model.number="editOrder.discountAmount" class="amount-input" type="number" placeholder="0" />
              </view>
              <view class="amount-row">
                <text class="amount-label">运费</text>
                <input v-model.number="editOrder.shippingFee" class="amount-input" type="number" placeholder="0" />
              </view>
              <view class="amount-row total-row">
                <text class="amount-label">实付金额</text>
                <text class="amount-value total">{{ formatMoney(calculateActualAmount()) }}</text>
              </view>
            </view>

            <!-- 备注信息 -->
            <view class="detail-section">
              <view class="section-title">备注信息</view>
              <view class="form-group">
                <textarea v-model="editOrder.remark" class="textarea" placeholder="请输入订单备注" />
              </view>
            </view>
          </scroll-view>
          
          <!-- 弹窗底部 -->
          <view class="detail-footer">
            <button class="btn btn-cancel" @click="closeDetailPopup">取消</button>
            <button class="btn btn-save" @click="saveOrderChanges" :loading="saving">保存</button>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { formatMoney, yuanToCents } from '@/utils/money.js'

export default {
  data() {
    return {
      selectedOrder: null,
      editOrder: {},
      saving: false,
      statusOptions: [
        { value: 'pending', label: '待支付' },
        { value: 'paid', label: '已支付' },
        { value: 'shipped', label: '已发货' },
        { value: 'completed', label: '已完成' },
        { value: 'cancelled', label: '已取消' },
        { value: 'refunded', label: '已退款' }
      ],
      // 测试订单数据
      testOrderData: {
        "_id": "test_order_id",
        "orderNo": "ORDER_1753520592882_j8wlod",
        "userId": "6875070f2eea65b0f16e9897",
        "totalFee": 60800,
        "goods": [
          {
            "_id": "68723b3c6e5d2d42e6a9d95f",
            "name": "冠能幼犬粮",
            "category": "狗狗主粮",
            "price": 25900,
            "stock": 100,
            "status": 1,
            "brief": "含初乳保护肠胃",
            "description": "商品详情",
            "cover_image": "https://mp-4a6e8f25-9d84-4913-93ea-8d2d63f1e453.cdn.bspapp.com/goods-image/12.jpg",
            "sales": 204,
            "create_date": 1431652354000,
            "num": 1
          },
          {
            "_id": "68723b37189f86d5e14e5ca9",
            "name": "伯纳天纯无谷",
            "category": "狗狗主粮",
            "price": 34900,
            "stock": 100,
            "status": 1,
            "brief": "鲜肉+蔬果配方",
            "description": "商品详情",
            "cover_image": "https://mp-4a6e8f25-9d84-4913-93ea-8d2d63f1e453.cdn.bspapp.com/goods-image/10.jpg",
            "sales": 147,
            "create_date": 1752052439500,
            "num": 1
          }
        ],
        "address": {
          "_id": "687626c78b0da441dca703f0",
          "name": "撒v成为v",
          "phone": "15894946891",
          "region": "山东省 东营市 利津县",
          "detail": "飒飒哥哥和",
          "is_default": true,
          "user_id": "6875070f2eea65b0f16e9897",
          "create_time": "2025-07-15T10:00:39.802Z",
          "update_time": "2025-07-15T12:22:58.079Z"
        },
        "status": "pending",
        "createTime": "2025-07-26T09:03:12.882Z",
        "subject": "购买2件商品",
        "discountAmount": 0,
        "shippingFee": 0,
        "remark": "这是一个测试订单的备注信息"
      }
    }
  },
  
  methods: {
    // 显示测试订单
    showTestOrder() {
      this.selectedOrder = this.testOrderData
      this.editOrder = JSON.parse(JSON.stringify(this.testOrderData))
      
      // 确保必要的字段存在
      if (!this.editOrder.address) {
        this.editOrder.address = {}
      }
      if (!this.editOrder.goods) {
        this.editOrder.goods = []
      }
      if (!this.editOrder.discountAmount) {
        this.editOrder.discountAmount = 0
      }
      if (!this.editOrder.shippingFee) {
        this.editOrder.shippingFee = 0
      }
      
      this.$refs.popup.open()
    },
    
    // 关闭详情弹窗
    closeDetailPopup() {
      this.$refs.popup.close()
      this.selectedOrder = null
      this.editOrder = {}
    },
    
    // 获取状态标签
    getStatusLabel(status) {
      const found = this.statusOptions.find(opt => opt.value === status)
      return found ? found.label : status
    },
    
    // 获取状态索引
    getStatusIndex(status) {
      return this.statusOptions.findIndex(item => item.value === status)
    },
    
    // 状态选择器变化
    onStatusPickerChange(e) {
      const index = e.detail.value
      this.editOrder.status = this.statusOptions[index].value
    },
    
    // 增加商品数量
    increaseQuantity(index) {
      this.editOrder.goods[index].num += 1
    },
    
    // 减少商品数量
    decreaseQuantity(index) {
      if (this.editOrder.goods[index].num > 1) {
        this.editOrder.goods[index].num -= 1
      }
    },
    
    // 移除商品
    removeGoods(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该商品吗？',
        success: (res) => {
          if (res.confirm) {
            this.editOrder.goods.splice(index, 1)
          }
        }
      })
    },
    
    // 计算商品总额
    calculateGoodsTotal() {
      let total = 0
      this.editOrder.goods.forEach(item => {
        total += (item.price || 0) * (item.num || 0)
      })
      return total
    },
    
    // 计算实际支付金额
    calculateActualAmount() {
      const goodsTotal = this.calculateGoodsTotal()
      const discount = this.editOrder.discountAmount || 0
      const shipping = this.editOrder.shippingFee || 0
      return Math.max(0, goodsTotal - discount + shipping)
    },
    
    // 保存订单修改
    async saveOrderChanges() {
      this.saving = true
      try {
        // 模拟保存过程
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        console.log('保存的订单数据:', this.editOrder)
        this.closeDetailPopup()
      } catch (error) {
        console.error('保存订单失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    },
    
    // 格式化金额显示
    formatMoney(cents) {
      return formatMoney(cents)
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
}

.test-section {
  text-align: center;
}

/* 订单详情弹窗样式 */
.order-detail-popup {
  background: #fff;
  border-radius: 16rpx;
  width: 90vw;
  max-width: 800rpx;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.detail-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.detail-content {
  flex: 1;
  padding: 0 40rpx;
  max-height: 60vh;
}

.detail-section {
  margin: 30rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2px solid #2979ff;
  display: inline-block;
}

.form-group {
  margin-bottom: 24rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.input, .textarea {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
}

.input:focus, .textarea:focus {
  border-color: #2979ff;
  outline: none;
}

.input.readonly {
  background: #f5f5f5;
  color: #999;
}

.textarea {
  min-height: 80rpx;
  resize: none;
}

.picker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
}

.goods-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
  background: #fafafa;
  position: relative;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.goods-category {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-brief {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.goods-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 28rpx;
  color: #ff4757;
  font-weight: bold;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 6rpx;
  overflow: hidden;
}

.qty-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border: none;
  font-size: 32rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qty-btn:active {
  background: #e0e0e0;
}

.qty-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: none;
  font-size: 28rpx;
  background: #fff;
}

.remove-goods-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 60rpx;
  height: 60rpx;
  background: #fff;
  border: 1px solid #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.amount-row.total-row {
  border-bottom: none;
  border-top: 2px solid #2979ff;
  padding-top: 20rpx;
  margin-top: 10rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.amount-value.total {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
}

.amount-input {
  width: 200rpx;
  padding: 12rpx;
  border: 1px solid #e0e0e0;
  border-radius: 6rpx;
  text-align: right;
  font-size: 28rpx;
}

.detail-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx 40rpx;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-save {
  background: #2979ff;
  color: #fff;
}

.btn:active {
  opacity: 0.8;
}
</style>
