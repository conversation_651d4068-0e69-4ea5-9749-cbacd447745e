<template>
  <view class="container">
    <view class="header">
      <text class="title">管理员订单测试</text>
    </view>
    
    <view class="test-section">
      <view class="section-title">测试获取全部订单（管理员模式）</view>
      <button type="primary" @click="testGetAllOrders" :loading="loading">获取全部订单</button>
      
      <view v-if="orders.length > 0" class="result-section">
        <text class="result-title">订单列表（共 {{ total }} 条）：</text>
        <view v-for="order in orders.slice(0, 5)" :key="order._id" class="order-item">
          <text>订单号: {{ order.orderNo }}</text>
          <text>用户ID: {{ order.userId }}</text>
          <text>状态: {{ order.status }}</text>
          <text>金额: ¥{{ order.totalAmount }}</text>
          <text>创建时间: {{ order.createTime }}</text>
        </view>
        <text v-if="orders.length > 5" class="more-text">... 还有 {{ orders.length - 5 }} 条订单</text>
      </view>
      
      <view v-if="error" class="error-section">
        <text class="error-text">错误: {{ error }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { useOrderStore } from '@/store/modules/order.js'
import { storeToRefs } from 'pinia'

export default {
  setup() {
    const orderStore = useOrderStore()
    const { orderList, total, loading } = storeToRefs(orderStore)
    const { fetchAllOrders } = orderStore

    return {
      orderStore,
      orderList,
      total,
      loading,
      fetchAllOrders
    }
  },
  
  data() {
    return {
      orders: [],
      error: null
    }
  },
  
  methods: {
    async testGetAllOrders() {
      try {
        this.error = null
        console.log('开始测试获取全部订单（管理员模式）...')
        
        // 不传入userId，触发管理员模式
        await this.fetchAllOrders({
          pageSize: 20
        })
        
        this.orders = this.orderList
        console.log('测试成功，获取到订单:', this.orders.length, '条')
        
        uni.showToast({
          title: `成功获取 ${this.total} 条订单`,
          icon: 'success'
        })
      } catch (error) {
        this.error = error.message
        console.error('测试失败:', error)
        uni.showToast({
          title: '测试失败',
          icon: 'error'
        })
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.result-section {
  margin-top: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.order-item {
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  border-left: 8rpx solid #007aff;
}

.order-item text {
  display: block;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.more-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  display: block;
  margin-top: 20rpx;
}

.error-section {
  margin-top: 40rpx;
  padding: 30rpx;
  background: #fff2f0;
  border-radius: 16rpx;
  border: 2rpx solid #ff4d4f;
}

.error-text {
  color: #ff4d4f;
  font-size: 28rpx;
}
</style>
