'use strict';

const db = uniCloud.database()
const addressesCollection = db.collection('addresses')

exports.main = async (event, context) => {
	const { action, data, address_id, user_id } = event
	
	if (!user_id) {
		return {
			code: 401,
			message: '用户未登录'
		}
	}
	
	switch (action) {
		case 'add':
			return await addAddress(data, user_id)
		case 'update':
			return await updateAddress(address_id, data, user_id)
		case 'delete':
			return await deleteAddress(address_id, user_id)
		case 'getAll':
			return await getAllAddresses(user_id)
		case 'getDefault':
			return await getDefaultAddress(user_id)
		case 'setDefault':
			return await setDefaultAddress(address_id, user_id)
		default:
			return {
				code: 400,
				message: '未知操作'
			}
	}
}

// 添加地址
async function addAddress(data, user_id) {
	try {
		const addressData = {
			...data,
			user_id,
			create_time: new Date(),
			update_time: new Date()
		}
		
		// 如果设置为默认地址，先取消其他默认地址
		if (data.is_default) {
			await addressesCollection.where({
				user_id,
				is_default: true
			}).update({
				is_default: false,
				update_time: new Date()
			})
		}
		
		const result = await addressesCollection.add(addressData)
		
		return {
			code: 200,
			message: '添加成功',
			data: result
		}
	} catch (error) {
		return {
			code: 500,
			message: '添加失败',
			error: error.message
		}
	}
}

// 更新地址
async function updateAddress(address_id, data, user_id) {
	try {
		const updateData = {
			...data,
			update_time: new Date()
		}
		
		// 如果设置为默认地址，先取消其他默认地址
		if (data.is_default) {
			await addressesCollection.where({
				user_id,
				is_default: true,
				_id: db.command.neq(address_id)
			}).update({
				is_default: false,
				update_time: new Date()
			})
		}
		
		const result = await addressesCollection.doc(address_id).update(updateData)
		
		return {
			code: 200,
			message: '更新成功',
			data: result
		}
	} catch (error) {
		return {
			code: 500,
			message: '更新失败',
			error: error.message
		}
	}
}

// 删除地址
async function deleteAddress(address_id, user_id) {
	try {
		const result = await addressesCollection.doc(address_id).remove()
		
		return {
			code: 200,
			message: '删除成功',
			data: result
		}
	} catch (error) {
		return {
			code: 500,
			message: '删除失败',
			error: error.message
		}
	}
}

// 获取用户所有地址
async function getAllAddresses(user_id) {
	try {
		const result = await addressesCollection
			.where({ user_id })
			.orderBy('is_default', 'desc')
			.orderBy('create_time', 'desc')
			.get()
		
		return {
			code: 200,
			message: '获取成功',
			data: result.data
		}
	} catch (error) {
		return {
			code: 500,
			message: '获取失败',
			error: error.message
		}
	}
}

// 获取默认地址
async function getDefaultAddress(user_id) {
	try {
		const result = await addressesCollection
			.where({
				user_id,
				is_default: true
			})
			.limit(1)
			.get()
		
		return {
			code: 200,
			message: '获取成功',
			data: result.data[0] || null
		}
	} catch (error) {
		return {
			code: 500,
			message: '获取失败',
			error: error.message
		}
	}
}

// 设置默认地址
async function setDefaultAddress(address_id, user_id) {
	try {
		// 先取消所有默认地址
		await addressesCollection.where({
			user_id,
			is_default: true
		}).update({
			is_default: false,
			update_time: new Date()
		})
		
		// 设置新的默认地址
		const result = await addressesCollection.doc(address_id).update({
			is_default: true,
			update_time: new Date()
		})
		
		return {
			code: 200,
			message: '设置成功',
			data: result
		}
	} catch (error) {
		return {
			code: 500,
			message: '设置失败',
			error: error.message
		}
	}
} 