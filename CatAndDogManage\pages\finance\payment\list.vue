<template>
  <view class="payment-list">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <uni-forms ref="form" :model="searchForm" label-width="80px">
        <uni-forms-item label="订单号">
          <uni-easyinput v-model="searchForm.orderNo" placeholder="请输入订单号" />
        </uni-forms-item>
        <uni-forms-item label="支付方式">
          <uni-data-select
            v-model="searchForm.paymentMethod"
            :localdata="paymentMethods"
            placeholder="请选择支付方式"
          />
        </uni-forms-item>
        <uni-forms-item label="支付状态">
          <uni-data-select
            v-model="searchForm.paymentStatus"
            :localdata="paymentStatuses"
            placeholder="请选择支付状态"
          />
        </uni-forms-item>
        <uni-forms-item label="时间范围">
          <uni-datetime-picker
            v-model="searchForm.dateRange"
            type="daterange"
            placeholder="请选择时间范围"
          />
        </uni-forms-item>
        <view class="search-buttons">
          <button type="primary" @click="handleSearch">搜索</button>
          <button @click="handleReset">重置</button>
        </view>
      </uni-forms>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar">
      <button type="primary" @click="handleAdd">新增支付记录</button>
      <button @click="handleExport">导出数据</button>
      <button @click="handleRefresh">刷新</button>
    </view>

    <!-- 数据表格 -->
    <uni-table
      ref="table"
      :data="tableData"
      :loading="loading"
      border
      stripe
      emptyText="暂无数据"
    >
      <uni-table-column type="selection" width="60" />
      <uni-table-column prop="orderNo" label="订单号" width="180" />
      <uni-table-column prop="amount" label="支付金额" width="120">
        <template #default="scope">
          <text class="amount">¥{{ scope.row.amount.toFixed(2) }}</text>
        </template>
      </uni-table-column>
      <uni-table-column prop="paymentMethod" label="支付方式" width="120">
        <template #default="scope">
          <uni-tag :text="getPaymentMethodText(scope.row.paymentMethod)" :type="getPaymentMethodType(scope.row.paymentMethod)" />
        </template>
      </uni-table-column>
      <uni-table-column prop="paymentStatus" label="支付状态" width="100">
        <template #default="scope">
          <uni-tag :text="getPaymentStatusText(scope.row.paymentStatus)" :type="getPaymentStatusType(scope.row.paymentStatus)" />
        </template>
      </uni-table-column>
      <uni-table-column prop="createTime" label="创建时间" width="180" />
      <uni-table-column prop="payTime" label="支付时间" width="180" />
      <uni-table-column prop="remark" label="备注" />
      <uni-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <view class="action-buttons">
            <button size="mini" type="primary" @click="handleView(scope.row)">查看</button>
            <button size="mini" type="default" @click="handleEdit(scope.row)">编辑</button>
            <button size="mini" type="warn" @click="handleDelete(scope.row)">删除</button>
          </view>
        </template>
      </uni-table-column>
    </uni-table>

    <!-- 分页 -->
    <uni-pagination
      :total="total"
      :pageSize="pageSize"
      :current="currentPage"
      @change="handlePageChange"
    />

    <!-- 新增/编辑弹窗 -->
    <uni-popup ref="formPopup" type="center" :mask-click="false">
      <view class="form-popup">
        <view class="popup-header">
          <text class="popup-title">{{ isEdit ? '编辑支付记录' : '新增支付记录' }}</text>
          <uni-icons type="close" size="20" @click="closeFormPopup" />
        </view>
        <uni-forms ref="formRef" :model="formData" :rules="formRules" label-width="100px">
          <uni-forms-item label="订单号" required>
            <uni-easyinput v-model="formData.orderNo" placeholder="请输入订单号" />
          </uni-forms-item>
          <uni-forms-item label="支付金额" required>
            <uni-number-box v-model="formData.amount" :min="0" :step="0.01" />
          </uni-forms-item>
          <uni-forms-item label="支付方式" required>
            <uni-data-select
              v-model="formData.paymentMethod"
              :localdata="paymentMethods"
              placeholder="请选择支付方式"
            />
          </uni-forms-item>
          <uni-forms-item label="支付状态" required>
            <uni-data-select
              v-model="formData.paymentStatus"
              :localdata="paymentStatuses"
              placeholder="请选择支付状态"
            />
          </uni-forms-item>
          <uni-forms-item label="支付时间">
            <uni-datetime-picker
              v-model="formData.payTime"
              type="datetime"
              placeholder="请选择支付时间"
            />
          </uni-forms-item>
          <uni-forms-item label="备注">
            <uni-easyinput
              v-model="formData.remark"
              type="textarea"
              placeholder="请输入备注信息"
            />
          </uni-forms-item>
        </uni-forms>
        <view class="popup-footer">
          <button @click="closeFormPopup">取消</button>
          <button type="primary" @click="handleSubmit">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'PaymentList',
  data() {
    return {
      // 搜索表单
      searchForm: {
        orderNo: '',
        paymentMethod: '',
        paymentStatus: '',
        dateRange: []
      },
      
      // 表格数据
      tableData: [],
      loading: false,
      total: 0,
      pageSize: 10,
      currentPage: 1,
      
      // 表单数据
      formData: {
        orderNo: '',
        amount: 0,
        paymentMethod: '',
        paymentStatus: '',
        payTime: '',
        remark: ''
      },
      isEdit: false,
      editId: null,
      
      // 支付方式选项
      paymentMethods: [
        { value: 'alipay', text: '支付宝' },
        { value: 'wechat', text: '微信支付' },
        { value: 'bank', text: '银行转账' },
        { value: 'cash', text: '现金支付' },
        { value: 'other', text: '其他' }
      ],
      
      // 支付状态选项
      paymentStatuses: [
        { value: 'pending', text: '待支付' },
        { value: 'success', text: '支付成功' },
        { value: 'failed', text: '支付失败' },
        { value: 'cancelled', text: '已取消' },
        { value: 'refunded', text: '已退款' }
      ],
      
      // 表单验证规则
      formRules: {
        orderNo: {
          rules: [
            { required: true, errorMessage: '请输入订单号' }
          ]
        },
        amount: {
          rules: [
            { required: true, errorMessage: '请输入支付金额' },
            { validateFunction: (rule, value, data, callback) => {
              if (value <= 0) {
                callback('支付金额必须大于0')
              }
              return true
            }}
          ]
        },
        paymentMethod: {
          rules: [
            { required: true, errorMessage: '请选择支付方式' }
          ]
        },
        paymentStatus: {
          rules: [
            { required: true, errorMessage: '请选择支付状态' }
          ]
        }
      }
    }
  },
  
  mounted() {
    this.loadData()
  },
  
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        // 模拟API调用
        await this.mockApiCall()
        this.loading = false
      } catch (error) {
        this.loading = false
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    
    // 模拟API调用
    mockApiCall() {
      return new Promise((resolve) => {
        setTimeout(() => {
          // 生成模拟数据
          const mockData = []
          for (let i = 1; i <= 20; i++) {
            mockData.push({
              id: i,
              orderNo: `ORDER${String(i).padStart(6, '0')}`,
              amount: Math.random() * 1000 + 100,
              paymentMethod: ['alipay', 'wechat', 'bank', 'cash', 'other'][Math.floor(Math.random() * 5)],
              paymentStatus: ['pending', 'success', 'failed', 'cancelled', 'refunded'][Math.floor(Math.random() * 5)],
              createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString(),
              payTime: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString() : '',
              remark: Math.random() > 0.5 ? '测试备注信息' : ''
            })
          }
          this.tableData = mockData
          this.total = 100
          resolve()
        }, 1000)
      })
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.loadData()
    },
    
    // 重置
    handleReset() {
      this.searchForm = {
        orderNo: '',
        paymentMethod: '',
        paymentStatus: '',
        dateRange: []
      }
      this.currentPage = 1
      this.loadData()
    },
    
    // 新增
    handleAdd() {
      this.isEdit = false
      this.editId = null
      this.formData = {
        orderNo: '',
        amount: 0,
        paymentMethod: '',
        paymentStatus: '',
        payTime: '',
        remark: ''
      }
      this.$refs.formPopup.open()
    },
    
    // 查看
    handleView(row) {
      uni.navigateTo({
        url: `/pages/finance/payment/detail?id=${row.id}`
      })
    },
    
    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.editId = row.id
      this.formData = { ...row }
      this.$refs.formPopup.open()
    },
    
    // 删除
    handleDelete(row) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除订单 ${row.orderNo} 的支付记录吗？`,
        success: (res) => {
          if (res.confirm) {
            // 执行删除操作
            const index = this.tableData.findIndex(item => item.id === row.id)
            if (index > -1) {
              this.tableData.splice(index, 1)
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
            }
          }
        }
      })
    },
    
    // 导出
    handleExport() {
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      })
    },
    
    // 刷新
    handleRefresh() {
      this.loadData()
    },
    
    // 分页变化
    handlePageChange(e) {
      this.currentPage = e.current
      this.loadData()
    },
    
    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.formRef.validate()
        if (valid) {
          if (this.isEdit) {
            // 编辑操作
            const index = this.tableData.findIndex(item => item.id === this.editId)
            if (index > -1) {
              this.tableData[index] = { ...this.formData, id: this.editId }
            }
          } else {
            // 新增操作
            const newId = Math.max(...this.tableData.map(item => item.id)) + 1
            this.tableData.unshift({
              ...this.formData,
              id: newId,
              createTime: new Date().toLocaleString()
            })
          }
          
          this.closeFormPopup()
          uni.showToast({
            title: this.isEdit ? '编辑成功' : '新增成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },
    
    // 关闭表单弹窗
    closeFormPopup() {
      this.$refs.formPopup.close()
    },
    
    // 获取支付方式文本
    getPaymentMethodText(method) {
      const item = this.paymentMethods.find(item => item.value === method)
      return item ? item.text : method
    },
    
    // 获取支付方式类型
    getPaymentMethodType(method) {
      const typeMap = {
        alipay: 'primary',
        wechat: 'success',
        bank: 'warning',
        cash: 'info',
        other: 'default'
      }
      return typeMap[method] || 'default'
    },
    
    // 获取支付状态文本
    getPaymentStatusText(status) {
      const item = this.paymentStatuses.find(item => item.value === status)
      return item ? item.text : status
    },
    
    // 获取支付状态类型
    getPaymentStatusType(status) {
      const typeMap = {
        pending: 'warning',
        success: 'success',
        failed: 'error',
        cancelled: 'info',
        refunded: 'default'
      }
      return typeMap[status] || 'default'
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-list {
  padding: 20px;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  
  button {
    flex: 1;
  }
}

.action-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  
  button {
    flex: 1;
  }
}

.amount {
  color: #f56c6c;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 5px;
  
  button {
    margin: 0;
  }
}

.form-popup {
  background: #fff;
  border-radius: 8px;
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.popup-title {
  font-size: 18px;
  font-weight: bold;
}

.popup-footer {
  display: flex;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
  
  button {
    flex: 1;
  }
}

:deep(.uni-forms) {
  padding: 20px;
}
</style> 