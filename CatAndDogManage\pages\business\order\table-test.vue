<template>
  <view class="test-page">
    <view class="page-header">
      <text class="page-title">订单表格测试</text>
      <text class="page-desc">测试表格的垂直水平居中效果</text>
    </view>
    
    <!-- 表格 -->
    <view class="order-table">
      <view class="table-header">
        <text class="col order-no">订单号</text>
        <text class="col customer">客户</text>
        <text class="col amount">金额</text>
        <text class="col status">状态</text>
        <text class="col date">下单时间</text>
        <text class="col actions">操作</text>
      </view>
      <view v-for="order in testOrders" :key="order._id" class="table-row">
        <text class="col order-no">{{ order.orderNo }}</text>
        <text class="col customer">{{ order.customer }}</text>
        <text class="col amount">{{ order.amount }}</text>
        <text class="col status">{{ order.status }}</text>
        <text class="col date">{{ order.date }}</text>
        <view class="col actions">
          <button class="edit-btn" size="mini">详情</button>
          <button class="delete-btn" size="mini">删除</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      testOrders: [
        {
          _id: '1',
          orderNo: 'ORDER_1753520592882_j8wlod',
          customer: '张三',
          amount: '¥608.00',
          status: '待支付',
          date: '2025/07/26 17:03'
        },
        {
          _id: '2',
          orderNo: 'ORDER_1753520586591_8ook6m',
          customer: '李四',
          amount: '¥9.00',
          status: '待支付',
          date: '2025/07/26 17:03'
        },
        {
          _id: '3',
          orderNo: 'ORDER_1753520581232_lrl3xi',
          customer: '王五',
          amount: '¥64.00',
          status: '待支付',
          date: '2025/07/26 17:03'
        },
        {
          _id: '4',
          orderNo: 'ORDER_1753520559353_0mrridc',
          customer: '赵六',
          amount: '¥427.00',
          status: '待支付',
          date: '2025/07/26 17:02'
        },
        {
          _id: '5',
          orderNo: 'ORDER_1752752996925_5s4wok',
          customer: '钱七',
          amount: '¥26300.00',
          status: '已支付',
          date: '2025/07/17 19:49'
        }
      ]
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 40rpx;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
}

/* 表格样式优化 - 垂直水平居中 */
.order-table {
  border: 1px solid #e0e0e0;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.table-header, .table-row {
  display: flex;
  align-items: center;
  min-height: 88rpx;
}

.table-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-weight: bold;
  min-height: 100rpx;
}

.table-header .col, .table-row .col {
  border-right: 1px solid rgba(224, 224, 224, 0.3);
  border-bottom: 1px solid #e0e0e0;
  padding: 20rpx 16rpx;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 88rpx;
  box-sizing: border-box;
}

.table-header .col {
  border-bottom: none;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
}

.table-header .col:last-child, .table-row .col:last-child {
  border-right: none;
}

.table-row:last-child .col {
  border-bottom: none;
}

.col {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}

/* 列宽度和对齐方式 */
.order-no { 
  flex: 2; 
  justify-content: flex-start !important;
  text-align: left !important;
}

.customer { 
  flex: 1.2; 
}

.amount { 
  flex: 1.2; 
  justify-content: center !important;
  text-align: center !important;
  font-weight: bold;
  color: #e91e63;
}

.status { 
  flex: 1; 
}

.date { 
  flex: 1.8; 
}

.actions { 
  flex: 2.8; 
  gap: 12rpx;
  min-width: 220rpx;
}

.table-row {
  transition: all 0.2s ease;
  min-height: 100rpx;
}

.table-row:hover {
  background: #f8f9fa;
  transform: translateY(-1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 表格操作按钮样式 */
.edit-btn {
  background: #2979ff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}

.edit-btn:hover {
  background: #1565c0;
  transform: translateY(-1rpx);
}

.delete-btn {
  background: #ff4757;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn:hover {
  background: #ff3742;
  transform: translateY(-1rpx);
}
</style>
